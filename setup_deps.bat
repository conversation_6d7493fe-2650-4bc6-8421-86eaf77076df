@echo off
echo.
echo ========================================
echo     Setup 7-Zip and bit7z Dependencies
echo ========================================
echo.

REM Create directory structure
echo [STEP 1] Creating directory structure...
if not exist third_party mkdir third_party
if not exist third_party\7zip mkdir third_party\7zip
if not exist third_party\bit7z mkdir third_party\bit7z
if not exist third_party\bit7z\include mkdir third_party\bit7z\include
if not exist third_party\bit7z\lib mkdir third_party\bit7z\lib

echo [OK] Directories created

REM Check for system 7z.dll
echo.
echo [STEP 2] Looking for 7z.dll...
set FOUND_7Z=0

if exist "C:\Program Files\7-Zip\7z.dll" (
    copy "C:\Program Files\7-Zip\7z.dll" "third_party\7zip\7z.dll" >nul
    set FOUND_7Z=1
    echo [OK] Copied 7z.dll from Program Files
)

if exist "C:\Program Files (x86)\7-Zip\7z.dll" if %FOUND_7Z%==0 (
    copy "C:\Program Files (x86)\7-Zip\7z.dll" "third_party\7zip\7z.dll" >nul
    set FOUND_7Z=1
    echo [OK] Copied 7z.dll from Program Files (x86)
)

if %FOUND_7Z%==0 (
    echo [WARNING] 7z.dll not found in system
    echo Please download 7-Zip from: https://www.7-zip.org/download.html
    echo Then copy 7z.dll to: third_party\7zip\7z.dll
)

REM Check for vcpkg bit7z
echo.
echo [STEP 3] Looking for bit7z...
set FOUND_BIT7Z=0

if exist "C:\vcpkg\installed\x64-windows\include\bit7z\bit7z.hpp" (
    xcopy "C:\vcpkg\installed\x64-windows\include\bit7z" "third_party\bit7z\include\bit7z\" /E /I /Y >nul
    copy "C:\vcpkg\installed\x64-windows\lib\bit7z.lib" "third_party\bit7z\lib\" >nul
    set FOUND_BIT7Z=1
    echo [OK] Copied bit7z from vcpkg
)

if %FOUND_BIT7Z%==0 (
    echo [INFO] bit7z not found in vcpkg
    echo For high performance, install bit7z:
    echo vcpkg install bit7z --triplet x64-windows
    echo Or download from: https://github.com/rikyoz/bit7z/releases
)

echo.
echo [STEP 4] Updating CMake configuration...
powershell -ExecutionPolicy Bypass -File update_cmake.ps1 2>nul
if %errorlevel% equ 0 (
    echo [OK] CMake configuration updated
) else (
    echo [WARNING] CMake update failed, using existing configuration
)

echo.
echo [STEP 5] Verification...
echo.

if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll: Available
) else (
    echo [MISSING] 7z.dll: Not found
)

if exist "third_party\bit7z\lib\bit7z.lib" (
    echo [OK] bit7z: Available (High performance mode)
) else (
    echo [INFO] bit7z: Not available (Will use 7z.exe fallback)
)

echo.
echo ========================================
echo     Integration Summary
echo ========================================
echo.

if exist "third_party\7zip\7z.dll" (
    echo Status: Ready for independent deployment
    echo.
    echo Features:
    echo - Embedded 7z.dll for compression
    if exist "third_party\bit7z\lib\bit7z.lib" (
        echo - High-performance bit7z library
    ) else (
        echo - Fallback to 7z.exe compatibility mode
    )
    echo - No external dependencies required
    echo - Can be distributed as standalone package
    echo.
    echo Next steps:
    echo 1. Clean build cache: Remove-Item -Recurse build -Force
    echo 2. Rebuild project: .\build_qt6.bat
    echo 3. Check build output for all DLL files
) else (
    echo Status: Manual setup required
    echo.
    echo Please follow manual_setup.md for detailed instructions
)

echo.
pause

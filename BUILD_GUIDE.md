# 好压万能压缩 - 构建指南

## 项目概述

这是一个基于Qt6和7-Zip技术的现代化压缩解压软件，界面设计参考了360压缩、好压等主流产品。

## 功能特性

### 🎨 界面设计
- **现代化卡片式布局** - 5个功能卡片，清晰直观
- **浅蓝色渐变主题** - 符合现代UI设计趋势
- **无边框窗口** - 自定义标题栏，更加美观
- **悬停动画效果** - 卡片阴影和颜色变化
- **响应式布局** - 支持窗口缩放

### 🔧 核心功能
1. **解压功能** (绿色卡片)
   - 支持格式：ZIP, RAR, 7Z, TAR, ISO
   - 拖拽解压支持
   - 进度显示

2. **压缩功能** (蓝色卡片)
   - 支持格式：ZIP, 7Z
   - 多文件选择
   - 压缩级别设置

3. **视频压缩** (橙色卡片) - 预留功能
4. **图片压缩** (紫色卡片) - 预留功能  
5. **PDF压缩** (红色卡片) - 预留功能

## 技术架构

### 核心组件
- **MainWindow** - 主窗口，负责整体布局和事件处理
- **FeatureCard** - 功能卡片组件，支持悬停动画
- **CompressionEngine** - 压缩引擎，基于7-Zip命令行工具
- **CompressionWorker** - 工作线程，避免UI阻塞

### 技术栈
- **Qt6** - 跨平台GUI框架
- **C++17** - 编程语言
- **CMake** - 构建系统
- **7-Zip** - 压缩解压核心

## 安装依赖

### 1. 安装Qt6

#### Windows (推荐)
```bash
# 下载Qt在线安装器
# https://www.qt.io/download-qt-installer

# 安装组件：
# - Qt 6.5.0 或更高版本
# - MinGW 11.2.0 64-bit 编译器
# - CMake
# - Ninja
```

#### 使用vcpkg (替代方案)
```bash
# 安装vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装Qt6
.\vcpkg install qt6[core,widgets] --triplet x64-windows
```

### 2. 安装7-Zip
```bash
# 下载并安装7-Zip
# https://www.7-zip.org/download.html
# 默认安装路径：C:\Program Files\7-Zip\
```

## 构建项目

### 方法1：使用Qt Creator (推荐)
1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择项目根目录的 `CMakeLists.txt`
4. 配置构建套件（MinGW 64-bit）
5. 点击构建按钮

### 方法2：命令行构建
```bash
# 设置Qt6路径（根据实际安装路径调整）
set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64

# 生成构建文件
cmake -B build -S . -G "MinGW Makefiles"

# 编译项目
cmake --build build --config Release

# 运行程序
.\build\BandiZip.exe
```

### 方法3：Visual Studio
```bash
# 生成Visual Studio项目
cmake -B build -S . -G "Visual Studio 17 2022"

# 打开解决方案
start build\BandiZip.sln
```

## 项目结构

```
BandiZip/
├── main.cpp                    # 应用程序入口
├── CMakeLists.txt             # 构建配置
├── BUILD_GUIDE.md             # 构建指南
├── src/                       # 源代码
│   ├── MainWindow.h/.cpp      # 主窗口
│   ├── FeatureCard.h/.cpp     # 功能卡片组件
│   └── CompressionEngine.h/.cpp # 压缩引擎
└── resources/                 # 资源文件
    ├── BandiZip.qrc          # Qt资源文件
    └── icons/                # 图标目录
        ├── README.txt        # 图标说明
        ├── bandizip.png      # 应用图标
        ├── extract.png       # 解压图标
        ├── compress.png      # 压缩图标
        ├── video.png         # 视频图标
        ├── image.png         # 图片图标
        └── pdf.png           # PDF图标
```

## 图标资源

当前使用的是占位符图标文件。为了获得最佳视觉效果，建议：

1. **下载高质量图标**
   - [Feather Icons](https://feathericons.com/)
   - [Heroicons](https://heroicons.com/)
   - [Tabler Icons](https://tabler-icons.io/)

2. **图标规格**
   - 格式：PNG
   - 尺寸：64x64像素
   - 背景：透明或对应主题色
   - 风格：扁平化设计

3. **颜色方案**
   - 解压: #00D4AA (绿色)
   - 压缩: #4A90E2 (蓝色)  
   - 视频: #FF8C42 (橙色)
   - 图片: #8B5CF6 (紫色)
   - PDF: #EF4444 (红色)

## 常见问题

### Q: CMake找不到Qt6
**A:** 设置CMAKE_PREFIX_PATH环境变量指向Qt6安装目录
```bash
set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64
```

### Q: 7-Zip未找到
**A:** 确保7-Zip安装在默认路径，或修改CompressionEngine.cpp中的路径

### Q: 编译错误
**A:** 检查Qt6版本是否为6.5+，确保安装了所有必需组件

### Q: 图标不显示
**A:** 替换resources/icons/目录中的占位符PNG文件为实际图标

## 开发计划

### 短期目标
- [x] 基础UI框架
- [x] 解压/压缩功能
- [ ] 完善图标资源
- [ ] 添加设置界面
- [ ] 文件关联功能

### 长期目标
- [ ] 视频压缩功能
- [ ] 图片压缩功能
- [ ] PDF压缩功能
- [ ] 云存储集成
- [ ] 多语言支持

## 许可证

本项目基于MIT许可证开源。

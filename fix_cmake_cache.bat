@echo off
echo.
echo ========================================
echo     Fix CMake Cache Issues
echo ========================================
echo.

echo [STEP 1] Cleaning all build directories...
if exist build rmdir /s /q build 2>nul
if exist cmake-build-debug rmdir /s /q cmake-build-debug 2>nul
if exist cmake-build-release rmdir /s /q cmake-build-release 2>nul
if exist cmake-build-debug-mingw rmdir /s /q cmake-build-debug-mingw 2>nul

echo [OK] Build directories cleaned

echo.
echo [STEP 2] Removing CMake cache files...
if exist CMakeCache.txt del CMakeCache.txt 2>nul
if exist CMakeFiles rmdir /s /q CMakeFiles 2>nul
if exist cmake_install.cmake del cmake_install.cmake 2>nul

echo [OK] CMake cache files removed

echo.
echo [STEP 3] Verifying CMakeLists.txt syntax...
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake not found in PATH
    echo Please ensure CMake is installed and in PATH
    goto :end
)

echo [OK] CMake is available

echo.
echo [STEP 4] Testing CMake configuration...
echo Note: Qt6 error is expected in command line test
cmake -B temp_test -S . 2>cmake_test.log
if exist temp_test rmdir /s /q temp_test 2>nul

findstr /C:"Configuring incomplete" cmake_test.log >nul
if %errorlevel% equ 0 (
    findstr /C:"Qt6" cmake_test.log >nul
    if %errorlevel% equ 0 (
        echo [OK] CMakeLists.txt syntax is correct (Qt6 missing is expected)
    ) else (
        echo [ERROR] CMakeLists.txt has syntax errors
        echo Error details:
        type cmake_test.log
        goto :cleanup
    )
) else (
    echo [ERROR] Unexpected CMake configuration result
    type cmake_test.log
    goto :cleanup
)

echo.
echo [SUCCESS] CMake cache issues fixed!
echo.
echo Next steps in Qt Creator:
echo 1. File → Open File or Project → CMakeLists.txt
echo 2. Select "Desktop Qt 6.9.0 MinGW 64-bit" kit
echo 3. Click "Configure Project"
echo 4. Build → Build All
echo.
echo If you still get cache errors:
echo 1. Go to Projects → Build Settings
echo 2. Set Build directory to: %CD%\build
echo 3. Click "Apply" and try building again

:cleanup
if exist cmake_test.log del cmake_test.log 2>nul

:end
echo.
pause

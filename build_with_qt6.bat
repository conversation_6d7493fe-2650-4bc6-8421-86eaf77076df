@echo off
echo.
echo ========================================
echo     Build BandiZip with Qt6
echo ========================================
echo.

REM Set Qt6 paths for your system
set QT6_ROOT=D:\Qt\6.9.0\mingw_64
set QT6_CMAKE=%QT6_ROOT%\lib\cmake\Qt6
set QT6_BIN=%QT6_ROOT%\bin
set MINGW_ROOT=D:\Qt\Tools\mingw1310_64
set MINGW_BIN=%MINGW_ROOT%\bin
set CMAKE_PREFIX_PATH=%QT6_ROOT%;%QT6_CMAKE%

echo [INFO] Build Configuration:
echo   Qt6 Root: %QT6_ROOT%
echo   Qt6 CMake: %QT6_CMAKE%
echo   Qt6 Bin: %QT6_BIN%
echo   MinGW Root: %MINGW_ROOT%
echo   MinGW Bin: %MINGW_BIN%
echo   CMAKE_PREFIX_PATH: %CMAKE_PREFIX_PATH%

REM Check if Qt6 exists
if not exist "%QT6_ROOT%\bin\qmake.exe" (
    echo [ERROR] Qt6 not found at: %QT6_ROOT%
    echo Please check your Qt6 installation path
    pause
    exit /b 1
)

echo [OK] Qt6 found at: %QT6_ROOT%

REM Check if MinGW exists
if not exist "%MINGW_BIN%\gcc.exe" (
    echo [ERROR] MinGW not found at: %MINGW_ROOT%
    echo Please check your MinGW installation path
    pause
    exit /b 1
)

echo [OK] MinGW found at: %MINGW_ROOT%

REM Add Qt6 and MinGW to PATH for this session
set PATH=%MINGW_BIN%;%QT6_BIN%;%PATH%

REM Clean previous build
if exist build-qt6 rmdir /s /q build-qt6 2>nul
mkdir build-qt6

echo.
echo [STEP 1] Running CMake configure...
cd build-qt6

cmake .. -G "MinGW Makefiles" ^
    -DCMAKE_BUILD_TYPE=Debug ^
    -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%" ^
    -DQt6_DIR="%QT6_CMAKE%" ^
    -DCMAKE_C_COMPILER="%MINGW_BIN%\gcc.exe" ^
    -DCMAKE_CXX_COMPILER="%MINGW_BIN%\g++.exe" ^
    -DCMAKE_MAKE_PROGRAM="%MINGW_BIN%\mingw32-make.exe"

if %errorlevel% neq 0 (
    echo [ERROR] CMake configure failed
    cd ..
    pause
    exit /b 1
)

echo [OK] CMake configure successful

echo.
echo [STEP 2] Building project...
cmake --build . --config Debug -j 4

if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    cd ..
    pause
    exit /b 1
)

echo [OK] Build successful

cd ..

echo.
echo [STEP 3] Copying 7z.dll...
if exist "third_party\7zip\7z.dll" (
    copy "third_party\7zip\7z.dll" "build-qt6\"
    if %errorlevel% equ 0 (
        echo [OK] 7z.dll copied to build directory
    ) else (
        echo [WARNING] Failed to copy 7z.dll
    )
) else (
    echo [WARNING] 7z.dll not found in third_party\7zip\
)

echo.
echo [STEP 4] Copying Qt6 DLLs...
if exist "%QT6_BIN%\Qt6Core.dll" (
    copy "%QT6_BIN%\Qt6Core.dll" "build-qt6\"
    copy "%QT6_BIN%\Qt6Gui.dll" "build-qt6\"
    copy "%QT6_BIN%\Qt6Widgets.dll" "build-qt6\"
    echo [OK] Qt6 DLLs copied
) else (
    echo [WARNING] Qt6 DLLs not found
)

echo.
echo [STEP 5] Copying Qt6 plugins...
if exist "%QT6_ROOT%\plugins\platforms" (
    if not exist "build-qt6\plugins\platforms" mkdir "build-qt6\plugins\platforms"
    copy "%QT6_ROOT%\plugins\platforms\qwindows.dll" "build-qt6\plugins\platforms\"
    echo [OK] Qt6 platform plugins copied
) else (
    echo [WARNING] Qt6 platform plugins not found
)

echo.
echo ========================================
echo     Build Complete!
echo ========================================
echo.
echo Executable: build-qt6\BandiZip.exe
echo.
echo To run the program:
echo   cd build-qt6
echo   .\BandiZip.exe
echo.
echo Or run directly:
echo   build-qt6\BandiZip.exe
echo.

REM Test if executable exists
if exist "build-qt6\BandiZip.exe" (
    echo [SUCCESS] BandiZip.exe created successfully!
    echo.
    echo Would you like to run it now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo Starting BandiZip...
        start "" "build-qt6\BandiZip.exe"
    )
) else (
    echo [ERROR] BandiZip.exe not found after build
)

echo.
pause

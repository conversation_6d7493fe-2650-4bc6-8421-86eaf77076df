@echo off
echo.
echo ========================================
echo     BandiZip - Qt6 Build Script
echo ========================================
echo.

REM Check CMake
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake not found
    echo Please install CMake 3.16+: https://cmake.org/download/
    goto :error
)

REM Check CMake version
for /f "tokens=3" %%i in ('cmake --version 2^>nul ^| findstr "cmake version"') do set CMAKE_VER=%%i
echo [OK] CMake %CMAKE_VER% found

REM Check Qt6
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Qt6 not found
    echo Please install Qt6 development environment
    echo Recommended version: Qt 6.5.3 LTS
    echo Download: https://www.qt.io/download-qt-installer
    goto :error
)

REM Verify Qt6 version
qmake --version | findstr "Qt version 6" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Detected Qt version may not be Qt6
    echo Please ensure Qt6.5.3+ is installed
)
echo [OK] Qt6 found

REM Check compiler
where gcc >nul 2>&1 || where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Compiler not found
    echo Please install MinGW (comes with Qt6) or Visual Studio
    goto :error
)
echo [OK] Compiler found

REM Check bit7z library
set BIT7Z_FOUND=0
if exist "C:\vcpkg\installed\x64-windows\include\bit7z\bit7z.hpp" (
    set BIT7Z_FOUND=1
    echo [OK] bit7z found via vcpkg
)
if exist "C:\bit7z\include\bit7z\bit7z.hpp" if %BIT7Z_FOUND%==0 (
    set BIT7Z_FOUND=1
    echo [OK] bit7z found at C:\bit7z
)

if %BIT7Z_FOUND%==0 (
    echo [INFO] bit7z not found, will use 7z.exe fallback
    echo [INFO] For better performance, install bit7z:
    echo [INFO] vcpkg install bit7z --triplet x64-windows
)

REM Check 7-Zip
if not exist "C:\Program Files\7-Zip\7z.exe" (
    if not exist "C:\Program Files (x86)\7-Zip\7z.exe" (
        echo [WARNING] 7-Zip not found
        echo Compression features may not work properly
        echo Download: https://www.7-zip.org/download.html
    ) else (
        echo [OK] 7-Zip found
    )
) else (
    echo [OK] 7-Zip found
)

echo.
echo [BUILD] Starting Qt6 project build...
echo.

REM Auto-detect Qt6 installation paths
set QT6_FOUND=0
set QT6_PATHS=C:\Qt\6.5.3\mingw_64 C:\Qt\6.6.0\mingw_64 C:\Qt\6.7.0\mingw_64 C:\Qt\6.8.0\mingw_64

for %%p in (%QT6_PATHS%) do (
    if exist "%%p" (
        set CMAKE_PREFIX_PATH=%%p
        set QT6_FOUND=1
        echo [SET] Qt6 path: %%p
        goto :qt6_found
    )
)

:qt6_found
if %QT6_FOUND%==0 (
    echo [WARNING] Qt6 installation path not auto-detected
    echo Please set CMAKE_PREFIX_PATH manually:
    echo set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64
)

REM Add bit7z path if found
if %BIT7Z_FOUND%==1 (
    if exist "C:\vcpkg\installed\x64-windows" (
        set CMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake
        echo [SET] Using vcpkg toolchain for bit7z
    )
    if exist "C:\bit7z" (
        set CMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH%;C:\bit7z
        echo [SET] Added bit7z path to CMAKE_PREFIX_PATH
    )
)

REM Clean old build files
if exist build (
    echo [CLEAN] Removing old build files...
    rmdir /s /q build 2>nul
)

REM Generate build files
echo [BUILD] Generating build files...
if defined CMAKE_TOOLCHAIN_FILE (
    cmake -B build -S . -G "MinGW Makefiles" -DCMAKE_TOOLCHAIN_FILE=%CMAKE_TOOLCHAIN_FILE% 2>build_error.log
) else (
    cmake -B build -S . -G "MinGW Makefiles" 2>build_error.log
)
if %errorlevel% neq 0 (
    echo [ERROR] Build configuration failed!
    echo.
    echo Error details:
    type build_error.log
    echo.
    echo Common solutions:
    echo 1. Set CMAKE_PREFIX_PATH: set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64
    echo 2. Ensure Qt6 is properly installed
    echo 3. Check compiler is in PATH
    goto :error
)
echo [OK] Build configuration successful

REM Compile project
echo [BUILD] Compiling project...
cmake --build build --config Release 2>compile_error.log
if %errorlevel% neq 0 (
    echo [ERROR] Compilation failed!
    echo.
    echo Error details:
    type compile_error.log
    echo.
    echo Common solutions:
    echo 1. Check Qt6 libraries are accessible
    echo 2. Verify compiler compatibility
    echo 3. Ensure sufficient disk space
    goto :error
)
echo [OK] Compilation successful

REM Check executable
set EXE_FOUND=0
if exist "build\BandiZip.exe" (
    set EXE_PATH=build\BandiZip.exe
    set EXE_FOUND=1
)
if exist "build\Release\BandiZip.exe" if %EXE_FOUND%==0 (
    set EXE_PATH=build\Release\BandiZip.exe
    set EXE_FOUND=1
)
if exist "build\Debug\BandiZip.exe" if %EXE_FOUND%==0 (
    set EXE_PATH=build\Debug\BandiZip.exe
    set EXE_FOUND=1
)

if %EXE_FOUND%==0 (
    echo [ERROR] Executable not found
    echo Build may have failed silently
    goto :error
)

echo.
echo [SUCCESS] Qt6 project build successful!
echo Executable: %EXE_PATH%
echo.

REM Deploy Qt6 dependencies (optional)
echo [DEPLOY] Checking Qt6 dependencies...
where windeployqt >nul 2>&1
if %errorlevel% equ 0 (
    echo [DEPLOY] Running windeployqt...
    windeployqt "%EXE_PATH%" --no-translations --no-system-d3d-compiler --no-opengl-sw
    echo [OK] Dependencies deployed
) else (
    echo [INFO] windeployqt not found, skipping dependency deployment
    echo [INFO] If the app fails to start, add Qt6 bin to PATH:
    echo [INFO] set PATH=%CMAKE_PREFIX_PATH%\bin;%%PATH%%
)

echo.
echo [LAUNCH] Starting application...
echo.

REM Run program
start "" "%EXE_PATH%"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to start application
    echo Try running directly: %EXE_PATH%
    echo Or check if Qt6 DLLs are accessible
    goto :error
)

echo [OK] Application started successfully!
echo.
echo Qt6 Features:
echo - Modern card-based UI with smooth animations
echo - Extract: ZIP, RAR, 7Z, TAR, ISO formats
echo - Compress: ZIP, 7Z formats
echo - Drag and drop support
echo - Multi-threaded processing
echo - Real-time progress display
echo.
echo Advanced Features (In Development):
echo - Video compression: MP4, WMV, AVI formats
echo - Image compression: JPG, PNG, GIF, BMP formats
echo - PDF compression: High-quality document compression
echo.
echo Usage Tips:
echo - Drag files directly to the window
echo - Click feature cards to select files
echo - Qt6 provides better performance and modern UI
echo.
goto :end

:error
echo.
echo Troubleshooting:
echo 1. Check QT6_SETUP_GUIDE.md for detailed installation steps
echo 2. Ensure Qt6.5.3+ is properly installed
echo 3. Verify CMAKE_PREFIX_PATH points to Qt6 installation
echo 4. Make sure MinGW compiler is in PATH
echo 5. Try using Qt Creator for easier setup
echo.
echo Quick fix commands:
echo set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64
echo set PATH=C:\Qt\6.5.3\mingw_64\bin;C:\Qt\Tools\mingw1120_64\bin;%%PATH%%
echo.
pause
exit /b 1

:end
echo Press any key to exit...
pause >nul

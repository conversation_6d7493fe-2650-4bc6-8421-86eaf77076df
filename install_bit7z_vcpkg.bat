@echo off
echo.
echo ========================================
echo     Install bit7z via vcpkg
echo ========================================
echo.

REM Check if vcpkg is available
where vcpkg >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] vcpkg not found in PATH
    echo.
    echo Please install vcpkg first:
    echo 1. git clone https://github.com/Microsoft/vcpkg.git
    echo 2. cd vcpkg
    echo 3. .\bootstrap-vcpkg.bat
    echo 4. Add vcpkg to PATH
    echo.
    goto :end
)

echo [OK] vcpkg found
echo.

REM Install bit7z
echo [INSTALL] Installing bit7z...
vcpkg install bit7z --triplet x64-windows
if %errorlevel% neq 0 (
    echo [ERROR] vcpkg installation failed
    goto :end
)

echo [OK] bit7z installed via vcpkg
echo.

REM Copy files to project
echo [COPY] Copying files to project...

REM Find vcpkg installation directory
set VCPKG_ROOT=
for /f "tokens=*" %%i in ('where vcpkg') do (
    for %%j in ("%%i") do set VCPKG_ROOT=%%~dpj
)

if "%VCPKG_ROOT%"=="" (
    echo [ERROR] Could not determine vcpkg root directory
    goto :end
)

set VCPKG_INSTALLED=%VCPKG_ROOT%installed\x64-windows

echo vcpkg root: %VCPKG_ROOT%
echo vcpkg installed: %VCPKG_INSTALLED%

REM Copy headers
if exist "%VCPKG_INSTALLED%\include\bit7z" (
    xcopy "%VCPKG_INSTALLED%\include\bit7z" "third_party\bit7z\include\bit7z\" /E /I /Y
    echo [OK] Headers copied
) else (
    echo [ERROR] bit7z headers not found in vcpkg
)

REM Copy library
if exist "%VCPKG_INSTALLED%\lib\bit7z.lib" (
    copy "%VCPKG_INSTALLED%\lib\bit7z.lib" "third_party\bit7z\lib\"
    echo [OK] Library copied
) else (
    echo [ERROR] bit7z.lib not found in vcpkg
)

REM Copy DLL if exists
if exist "%VCPKG_INSTALLED%\bin\bit7z.dll" (
    copy "%VCPKG_INSTALLED%\bin\bit7z.dll" "third_party\bit7z\lib\"
    echo [OK] DLL copied
)

goto :verify

:end
echo.
echo Alternative: Download manually from GitHub
echo https://github.com/rikyoz/bit7z/releases
pause
exit /b 1

:verify
echo.
echo [VERIFY] Checking installation...
echo.

if exist "third_party\bit7z\include\bit7z\bit7z.hpp" (
    echo [OK] bit7z.hpp found
) else (
    echo [ERROR] bit7z.hpp missing
)

if exist "third_party\bit7z\lib\bit7z.lib" (
    echo [OK] bit7z.lib found
) else (
    echo [ERROR] bit7z.lib missing
)

if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll found
) else (
    echo [ERROR] 7z.dll missing
)

echo.
echo [SUCCESS] bit7z integration complete!
echo.
echo Next steps:
echo 1. Update CMake: .\update_cmake.ps1
echo 2. Clean build: Remove-Item -Recurse build -Force
echo 3. Rebuild: .\build_qt6.bat
echo.
pause

@echo off
echo.
echo ========================================
echo     Debug 7z.dll and 7z.exe Paths
echo ========================================
echo.

echo [INFO] Checking 7z.dll locations...
echo.

REM Check 7z.dll in build directory
if exist "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll" (
    echo [OK] 7z.dll found in: build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll
    dir "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll" | findstr /C:"7z.dll"
) else (
    echo [MISSING] 7z.dll not found in build directory
)

REM Check 7z.dll in third_party
if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll found in: third_party\7zip\7z.dll
    dir "third_party\7zip\7z.dll" | findstr /C:"7z.dll"
) else (
    echo [MISSING] 7z.dll not found in third_party
)

echo.
echo [INFO] Checking 7z.exe locations...
echo.

REM Check system 7z.exe
if exist "C:\Program Files\7-Zip\7z.exe" (
    echo [OK] 7z.exe found in: C:\Program Files\7-Zip\7z.exe
) else (
    echo [MISSING] 7z.exe not found in Program Files
)

if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    echo [OK] 7z.exe found in: C:\Program Files (x86)\7-Zip\7z.exe
) else (
    echo [MISSING] 7z.exe not found in Program Files (x86)
)

echo.
echo [INFO] Application directory paths...
echo Current directory: %CD%
echo.

echo [SOLUTION] Copy 7z.dll to application directory:
if exist "third_party\7zip\7z.dll" (
    copy "third_party\7zip\7z.dll" "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll"
    if %errorlevel% equ 0 (
        echo [OK] 7z.dll copied to application directory
    ) else (
        echo [ERROR] Failed to copy 7z.dll
    )
) else (
    echo [ERROR] Source 7z.dll not found
)

echo.
echo [SOLUTION] Install 7-Zip if not available:
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] 7z.exe is available in PATH
) else (
    echo [INFO] 7z.exe not in PATH, but may be in standard location
)

echo.
pause

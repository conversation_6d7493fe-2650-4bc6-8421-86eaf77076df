@echo off
echo.
echo ========================================
echo     Download 7-Zip SDK Headers
echo ========================================
echo.

echo [INFO] bit7z requires 7-Zip SDK headers to compile
echo.

REM Create SDK directory
if not exist "third_party\7zip-sdk" mkdir "third_party\7zip-sdk"

echo [STEP 1] Downloading 7-Zip SDK...
echo.

REM Try to download 7-Zip SDK using PowerShell
powershell -Command "try { Invoke-WebRequest -Uri 'https://www.7-zip.org/a/lzma2301.7z' -OutFile 'third_party\7zip-sdk\lzma-sdk.7z' -UseBasicParsing; Write-Host '[OK] SDK downloaded' } catch { Write-Host '[ERROR] Download failed:' $_.Exception.Message }"

if not exist "third_party\7zip-sdk\lzma-sdk.7z" (
    echo [ERROR] Failed to download 7-Zip SDK
    echo.
    echo Manual download required:
    echo 1. Visit: https://www.7-zip.org/sdk.html
    echo 2. Download LZMA SDK
    echo 3. Extract to third_party\7zip-sdk\
    goto :end
)

echo.
echo [STEP 2] Extracting SDK...

REM Check if 7z.exe is available for extraction
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Using 7z.exe to extract...
    7z x "third_party\7zip-sdk\lzma-sdk.7z" -o"third_party\7zip-sdk\" -y
    if %errorlevel% equ 0 (
        echo [OK] SDK extracted successfully
    ) else (
        echo [ERROR] Extraction failed
        goto :manual
    )
) else (
    echo [WARNING] 7z.exe not found
    goto :manual
)

echo.
echo [STEP 3] Verifying SDK files...

if exist "third_party\7zip-sdk\CPP\Common\MyCom.h" (
    echo [OK] MyCom.h found
) else (
    echo [ERROR] MyCom.h not found
    goto :manual
)

if exist "third_party\7zip-sdk\CPP\7zip" (
    echo [OK] 7zip headers found
) else (
    echo [ERROR] 7zip headers not found
    goto :manual
)

echo.
echo [SUCCESS] 7-Zip SDK ready!
echo.
echo Next steps:
echo 1. Update CMakeLists.txt to include SDK paths
echo 2. Rebuild the project
echo.
goto :end

:manual
echo.
echo [MANUAL] Manual extraction required:
echo.
echo 1. Download 7-Zip software from: https://www.7-zip.org/
echo 2. Use it to extract: third_party\7zip-sdk\lzma-sdk.7z
echo 3. Ensure the following structure exists:
echo    third_party\7zip-sdk\CPP\Common\MyCom.h
echo    third_party\7zip-sdk\CPP\7zip\...
echo.

:end
pause

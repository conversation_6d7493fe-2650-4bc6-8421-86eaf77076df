# 自动下载和集成7-Zip和bit7z依赖
param(
    [switch]$Force = $false
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    依赖库自动集成脚本" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$ErrorActionPreference = "Stop"

# 创建目录结构
$Directories = @(
    "third_party",
    "third_party\7zip",
    "third_party\bit7z",
    "third_party\bit7z\include",
    "third_party\bit7z\lib"
)

foreach ($dir in $Directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ 创建目录: $dir" -ForegroundColor Green
    }
}

# 下载7-Zip DLL
Write-Host ""
Write-Host "📥 下载7-Zip依赖..." -ForegroundColor Cyan

$SevenZipUrl = "https://www.7-zip.org/a/7z2301-extra.7z"
$SevenZipFile = "third_party\7zip\7z-extra.7z"
$SevenZipDll = "third_party\7zip\7z.dll"

if (-not (Test-Path $SevenZipDll) -or $Force) {
    try {
        Write-Host "下载7-Zip额外文件..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $SevenZipUrl -OutFile $SevenZipFile -UseBasicParsing
        
        # 解压7z.dll
        if (Get-Command "7z" -ErrorAction SilentlyContinue) {
            & 7z e $SevenZipFile -o"third_party\7zip" 7z.dll -y
        } else {
            Write-Host "⚠️  需要7-Zip来解压文件，请手动解压7z.dll" -ForegroundColor Yellow
        }
        
        if (Test-Path $SevenZipDll) {
            Write-Host "✅ 7z.dll 下载成功" -ForegroundColor Green
        }
        
        # 清理临时文件
        Remove-Item $SevenZipFile -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ 下载7-Zip失败: $_" -ForegroundColor Red
        Write-Host "请手动下载7z.dll到 third_party\7zip\" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ 7z.dll 已存在" -ForegroundColor Green
}

# 下载bit7z预编译版本
Write-Host ""
Write-Host "📥 下载bit7z依赖..." -ForegroundColor Cyan

$Bit7zUrl = "https://github.com/rikyoz/bit7z/releases/latest/download/bit7z-windows-x64.zip"
$Bit7zFile = "third_party\bit7z\bit7z.zip"
$Bit7zLib = "third_party\bit7z\lib\bit7z.lib"

if (-not (Test-Path $Bit7zLib) -or $Force) {
    try {
        Write-Host "下载bit7z预编译版本..." -ForegroundColor Yellow
        
        # 获取最新版本信息
        $LatestRelease = Invoke-RestMethod -Uri "https://api.github.com/repos/rikyoz/bit7z/releases/latest"
        $DownloadUrl = $LatestRelease.assets | Where-Object { $_.name -like "*windows*x64*" } | Select-Object -First 1 -ExpandProperty browser_download_url
        
        if ($DownloadUrl) {
            Invoke-WebRequest -Uri $DownloadUrl -OutFile $Bit7zFile -UseBasicParsing
            
            # 解压bit7z文件
            Expand-Archive -Path $Bit7zFile -DestinationPath "third_party\bit7z\temp" -Force
            
            # 移动文件到正确位置
            $TempDir = Get-ChildItem "third_party\bit7z\temp" | Select-Object -First 1
            if ($TempDir) {
                Copy-Item "$($TempDir.FullName)\include\*" "third_party\bit7z\include\" -Recurse -Force
                Copy-Item "$($TempDir.FullName)\lib\*" "third_party\bit7z\lib\" -Recurse -Force
            }
            
            # 清理临时文件
            Remove-Item "third_party\bit7z\temp" -Recurse -Force -ErrorAction SilentlyContinue
            Remove-Item $Bit7zFile -ErrorAction SilentlyContinue
            
            Write-Host "✅ bit7z 下载成功" -ForegroundColor Green
        } else {
            throw "未找到bit7z Windows x64版本"
        }
        
    } catch {
        Write-Host "❌ 下载bit7z失败: $_" -ForegroundColor Red
        Write-Host "将使用vcpkg或手动安装方式" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ bit7z 已存在" -ForegroundColor Green
}

# 创建依赖信息文件
Write-Host ""
Write-Host "📝 创建依赖信息文件..." -ForegroundColor Cyan

$DependencyInfo = @"
# 第三方依赖库信息

## 7-Zip
- 版本: 23.01
- 文件: third_party/7zip/7z.dll
- 许可证: LGPL
- 用途: 压缩解压核心引擎

## bit7z
- 版本: 最新版
- 文件: third_party/bit7z/
- 许可证: MPL-2.0
- 用途: 现代C++压缩库接口

## 使用说明
1. 这些文件会自动复制到构建输出目录
2. 支持独立部署，无需额外安装
3. bit7z提供高性能接口，7z.dll作为回退方案

## 更新依赖
运行以下命令更新依赖库：
```
.\setup_dependencies.ps1 -Force
```
"@

$DependencyInfo | Out-File -FilePath "third_party\README.md" -Encoding UTF8

Write-Host "✅ 依赖信息文件已创建" -ForegroundColor Green

# 验证集成结果
Write-Host ""
Write-Host "🔍 验证集成结果..." -ForegroundColor Cyan

$Results = @()
$Results += [PSCustomObject]@{
    Component = "7z.dll"
    Path = "third_party\7zip\7z.dll"
    Status = if (Test-Path "third_party\7zip\7z.dll") { "✅ 已集成" } else { "❌ 缺失" }
}

$Results += [PSCustomObject]@{
    Component = "bit7z头文件"
    Path = "third_party\bit7z\include"
    Status = if (Test-Path "third_party\bit7z\include\bit7z") { "✅ 已集成" } else { "❌ 缺失" }
}

$Results += [PSCustomObject]@{
    Component = "bit7z库文件"
    Path = "third_party\bit7z\lib"
    Status = if (Test-Path "third_party\bit7z\lib\bit7z.lib") { "✅ 已集成" } else { "❌ 缺失" }
}

$Results | Format-Table -AutoSize

Write-Host ""
if ($Results | Where-Object { $_.Status -like "*缺失*" }) {
    Write-Host "⚠️  部分依赖缺失，请检查网络连接或手动下载" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "手动下载链接:" -ForegroundColor Yellow
    Write-Host "7-Zip: https://www.7-zip.org/download.html" -ForegroundColor Cyan
    Write-Host "bit7z: https://github.com/rikyoz/bit7z/releases" -ForegroundColor Cyan
} else {
    Write-Host "🎉 所有依赖已成功集成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步:" -ForegroundColor Cyan
    Write-Host "1. 运行 .\update_cmake.ps1 更新CMake配置" -ForegroundColor White
    Write-Host "2. 重新构建项目" -ForegroundColor White
    Write-Host "3. 项目现在可以独立部署" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

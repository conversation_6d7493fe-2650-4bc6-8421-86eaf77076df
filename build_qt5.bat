@echo off
echo.
echo ========================================
echo     BandiZip - Qt5 Build Script
echo ========================================
echo.

REM Check CMake
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake not found
    echo Please install CMake: https://cmake.org/download/
    goto :error
)
echo [OK] CMake found

REM Check Qt5
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Qt5 not found
    echo Please install Qt5 development environment
    echo Recommended version: Qt 5.15.2
    echo Download: https://www.qt.io/download-qt-installer
    goto :error
)
echo [OK] Qt5 found

REM Check compiler
where gcc >nul 2>&1 || where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Compiler not found
    echo Please install MinGW or Visual Studio
    goto :error
)
echo [OK] Comp<PERSON> found

REM Check 7-Zip
if not exist "C:\Program Files\7-Zip\7z.exe" (
    if not exist "C:\Program Files (x86)\7-Zip\7z.exe" (
        echo [WARNING] 7-Zip not found
        echo Compression features may not work
        echo Download: https://www.7-zip.org/download.html
    ) else (
        echo [OK] 7-Zip found
    )
) else (
    echo [OK] 7-Zip found
)

echo.
echo [BUILD] Starting Qt5 project build...
echo.

REM Set Qt5 path
set QT5_FOUND=0
if exist "C:\Qt\5.15.2\mingw81_64" (
    set CMAKE_PREFIX_PATH=C:\Qt\5.15.2\mingw81_64
    set QT5_FOUND=1
    echo [SET] Qt5 path: C:\Qt\5.15.2\mingw81_64
)
if exist "C:\Qt\5.15.1\mingw81_64" if %QT5_FOUND%==0 (
    set CMAKE_PREFIX_PATH=C:\Qt\5.15.1\mingw81_64
    set QT5_FOUND=1
    echo [SET] Qt5 path: C:\Qt\5.15.1\mingw81_64
)

REM Clean old build files
if exist build (
    echo [CLEAN] Removing old build files...
    rmdir /s /q build
)

REM Generate build files
echo [BUILD] Generating build files...
cmake -B build -S . -G "MinGW Makefiles" 2>build_error.log
if %errorlevel% neq 0 (
    echo [ERROR] Build configuration failed!
    echo Error details:
    type build_error.log
    goto :error
)
echo [OK] Build configuration successful

REM Compile project
echo [BUILD] Compiling project...
cmake --build build --config Release 2>compile_error.log
if %errorlevel% neq 0 (
    echo [ERROR] Compilation failed!
    echo Error details:
    type compile_error.log
    goto :error
)
echo [OK] Compilation successful

REM Check executable
set EXE_FOUND=0
if exist "build\BandiZip.exe" (
    set EXE_PATH=build\BandiZip.exe
    set EXE_FOUND=1
)
if exist "build\Release\BandiZip.exe" if %EXE_FOUND%==0 (
    set EXE_PATH=build\Release\BandiZip.exe
    set EXE_FOUND=1
)

if %EXE_FOUND%==0 (
    echo [ERROR] Executable not found
    goto :error
)

echo.
echo [SUCCESS] Qt5 project build successful!
echo.
echo [LAUNCH] Starting application...
echo.

REM Run program
start "" "%EXE_PATH%"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to start application
    echo Try running directly: %EXE_PATH%
    goto :error
)

echo [OK] Application started!
echo.
echo Features:
echo - Extract: ZIP, RAR, 7Z, TAR, ISO formats
echo - Compress: ZIP, 7Z formats
echo - Video compression: In development
echo - Image compression: In development
echo - PDF compression: In development
echo.
echo Usage:
echo - Drag and drop files to the window
echo - Click feature cards to select files
echo - Qt5 version has better compatibility
echo.
goto :end

:error
echo.
echo Solutions:
echo 1. Check QT5_INSTALL_GUIDE.md for Qt5 installation
echo 2. Ensure all dependencies are correctly installed
echo 3. Verify environment variables are set correctly
echo.
pause
exit /b 1

:end
echo Press any key to exit...
pause >nul

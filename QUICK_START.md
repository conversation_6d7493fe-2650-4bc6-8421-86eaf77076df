# 🚀 快速启动指南

## 当前状态检查

✅ CMake 4.0.2 已安装  
❌ Qt6 未找到  
❌ 编译器未找到  

## 🛠️ 安装开发环境

### 方案1：Qt官方安装器（推荐）

1. **下载Qt安装器**
   ```
   访问：https://www.qt.io/download-qt-installer
   下载：qt-unified-windows-x64-online.exe
   ```

2. **安装Qt6组件**
   - Qt 6.5.0 或更高版本
   - MinGW 11.2.0 64-bit 编译器
   - CMake 工具
   - Qt Creator IDE

3. **设置环境变量**
   ```cmd
   # 添加到系统PATH
   C:\Qt\6.5.0\mingw_64\bin
   C:\Qt\Tools\mingw1120_64\bin
   C:\Qt\Tools\CMake_64\bin
   ```

### 方案2：使用Visual Studio

1. **安装Visual Studio 2022**
   ```
   访问：https://visualstudio.microsoft.com/downloads/
   选择：Community版本（免费）
   工作负载：使用C++的桌面开发
   ```

2. **安装Qt6**
   ```cmd
   # 使用vcpkg包管理器
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   .\vcpkg install qt6[core,widgets] --triplet x64-windows
   ```

### 方案3：使用MSYS2（轻量级）

1. **安装MSYS2**
   ```
   访问：https://www.msys2.org/
   下载并安装MSYS2
   ```

2. **安装Qt6和编译器**
   ```bash
   # 在MSYS2终端中执行
   pacman -S mingw-w64-x86_64-qt6-base
   pacman -S mingw-w64-x86_64-qt6-tools
   pacman -S mingw-w64-x86_64-cmake
   pacman -S mingw-w64-x86_64-gcc
   ```

## 🏃‍♂️ 快速启动步骤

### 安装完成后执行：

1. **验证安装**
   ```cmd
   qmake --version
   cmake --version
   gcc --version  # 或 cl 如果使用VS
   ```

2. **设置Qt路径**
   ```cmd
   # 根据实际安装路径调整
   set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64
   ```

3. **构建项目**
   ```cmd
   # 生成构建文件
   cmake -B build -S . -G "MinGW Makefiles"
   
   # 编译项目
   cmake --build build --config Release
   
   # 运行程序
   .\build\BandiZip.exe
   ```

## 🎯 一键启动脚本

创建 `start.bat` 文件：

```batch
@echo off
echo 正在启动好压万能压缩项目...

REM 检查Qt6是否安装
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Qt6，请先安装Qt6开发环境
    echo 请参考 QUICK_START.md 中的安装指南
    pause
    exit /b 1
)

REM 设置Qt路径（根据实际安装路径调整）
set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64

REM 清理旧的构建文件
if exist build rmdir /s /q build

REM 生成构建文件
echo 正在生成构建文件...
cmake -B build -S . -G "MinGW Makefiles"
if %errorlevel% neq 0 (
    echo 构建配置失败！
    pause
    exit /b 1
)

REM 编译项目
echo 正在编译项目...
cmake --build build --config Release
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

REM 运行程序
echo 启动程序...
.\build\BandiZip.exe

pause
```

## 🔧 故障排除

### 常见问题及解决方案

1. **CMake找不到Qt6**
   ```
   错误：Could not find a package configuration file provided by "Qt6"
   解决：设置 CMAKE_PREFIX_PATH 环境变量
   ```

2. **编译器未找到**
   ```
   错误：No CMAKE_CXX_COMPILER could be found
   解决：安装MinGW或Visual Studio编译器
   ```

3. **7-Zip未找到**
   ```
   错误：7-Zip not found
   解决：下载安装7-Zip到默认路径 C:\Program Files\7-Zip\
   ```

4. **图标不显示**
   ```
   问题：界面图标显示为空白
   解决：替换 resources/icons/ 中的PNG文件为实际图标
   ```

## 📱 使用Qt Creator（推荐）

如果安装了Qt Creator：

1. **打开项目**
   - 启动Qt Creator
   - 选择 "Open Project"
   - 选择项目根目录的 `CMakeLists.txt`

2. **配置构建套件**
   - 选择MinGW 64-bit套件
   - 确认CMake配置正确

3. **构建运行**
   - 点击构建按钮（锤子图标）
   - 点击运行按钮（绿色三角形）

## 🎉 成功启动后

程序启动后您将看到：
- 现代化的卡片式界面
- 5个功能模块（解压、压缩、视频、图片、PDF）
- 浅蓝色渐变背景
- 可拖拽的文件操作

立即可以使用的功能：
- ✅ 解压 ZIP、RAR、7Z、TAR、ISO 文件
- ✅ 压缩文件为 ZIP、7Z 格式
- ✅ 拖拽文件到窗口进行操作

## 📞 需要帮助？

如果遇到问题：
1. 检查 BUILD_GUIDE.md 中的详细说明
2. 确认所有依赖都已正确安装
3. 检查环境变量设置是否正确

# Manual UnRAR.dll setup guide for BandiZip RAR5 support
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     UnRAR.dll Setup Guide" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Your BandiZip has detected a RAR5 file but UnRAR.dll is missing." -ForegroundColor Yellow
Write-Host "To enable RAR5 support, you need to obtain UnRAR.dll." -ForegroundColor Yellow
Write-Host ""

Write-Host "Option 1: Install WinRAR (Easiest)" -ForegroundColor Green
Write-Host "1. Download WinRAR from: https://www.win-rar.com/download.html" -ForegroundColor White
Write-Host "2. Install WinRAR normally" -ForegroundColor White
Write-Host "3. UnRAR.dll will be automatically detected" -ForegroundColor White
Write-Host "4. Restart BandiZip - RAR5 support will work!" -ForegroundColor White
Write-Host ""

Write-Host "Option 2: Manual UnRAR.dll placement" -ForegroundColor Green
Write-Host "1. Obtain UnRAR.dll from a WinRAR installation" -ForegroundColor White
Write-Host "2. Copy UnRAR.dll to one of these locations:" -ForegroundColor White
Write-Host "   - build\UnRAR.dll" -ForegroundColor Cyan
Write-Host "   - third_party\unrar\UnRAR.dll" -ForegroundColor Cyan
Write-Host "3. Restart BandiZip" -ForegroundColor White
Write-Host ""

Write-Host "Option 3: Use alternative formats" -ForegroundColor Green
Write-Host "1. Extract the RAR5 file using WinRAR or 7-Zip" -ForegroundColor White
Write-Host "2. Re-compress as ZIP or 7Z format" -ForegroundColor White
Write-Host "3. Use BandiZip to extract the new archive" -ForegroundColor White
Write-Host ""

Write-Host "Current Status:" -ForegroundColor Yellow
Write-Host "✅ BandiZip is working perfectly" -ForegroundColor Green
Write-Host "✅ ZIP/7Z formats fully supported" -ForegroundColor Green
Write-Host "✅ RAR4 format supported" -ForegroundColor Green
Write-Host "❌ RAR5 format needs UnRAR.dll" -ForegroundColor Red
Write-Host ""

Write-Host "After obtaining UnRAR.dll:" -ForegroundColor Cyan
Write-Host "✅ Complete RAR5 support" -ForegroundColor Green
Write-Host "✅ All RAR variants supported" -ForegroundColor Green
Write-Host "✅ Encrypted RAR support" -ForegroundColor Green
Write-Host "✅ Pure bit7z + DLL implementation" -ForegroundColor Green
Write-Host ""

# Check if we can find any existing UnRAR.dll
$possiblePaths = @(
    "C:\Program Files\WinRAR\UnRAR.dll",
    "C:\Program Files (x86)\WinRAR\UnRAR.dll",
    "D:\Program Files\WinRAR\UnRAR.dll",
    "D:\Program Files (x86)\WinRAR\UnRAR.dll"
)

$foundPath = $null
foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $foundPath = $path
        break
    }
}

if ($foundPath) {
    Write-Host "🎉 FOUND UnRAR.dll at: $foundPath" -ForegroundColor Green
    Write-Host ""
    $copy = Read-Host "Copy to BandiZip project? (y/n)"
    
    if ($copy -eq 'y' -or $copy -eq 'Y') {
        # Create target directory
        $targetDir = "third_party\unrar"
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Copy UnRAR.dll
        Copy-Item $foundPath "$targetDir\UnRAR.dll" -Force
        Write-Host "✅ UnRAR.dll copied to: $targetDir\UnRAR.dll" -ForegroundColor Green
        
        # Copy to build directory
        if (Test-Path "build") {
            Copy-Item $foundPath "build\UnRAR.dll" -Force
            Write-Host "✅ UnRAR.dll copied to: build\UnRAR.dll" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "🎉 SUCCESS! UnRAR.dll is now available!" -ForegroundColor Green
        Write-Host "Restart BandiZip to enable RAR5 support." -ForegroundColor Yellow
    }
} else {
    Write-Host "ℹ️ No existing UnRAR.dll found on system." -ForegroundColor Blue
    Write-Host "Please follow Option 1 or 2 above to obtain UnRAR.dll." -ForegroundColor Blue
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

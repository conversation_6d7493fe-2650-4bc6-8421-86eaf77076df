# PowerShell script to setup UnRAR.dll for bit7z RAR5 support
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     Setup UnRAR.dll for BandiZip" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "UnRAR.dll will enable full RAR support including RAR5 format!" -ForegroundColor Green
Write-Host ""

# Check if WinRAR is installed (contains UnRAR.dll)
$winrarPaths = @(
    "C:\Program Files\WinRAR\UnRAR.dll",
    "C:\Program Files (x86)\WinRAR\UnRAR.dll",
    "D:\Program Files\WinRAR\UnRAR.dll",
    "D:\Program Files (x86)\WinRAR\UnRAR.dll"
)

$foundUnRAR = $null
foreach ($path in $winrarPaths) {
    if (Test-Path $path) {
        $foundUnRAR = $path
        break
    }
}

if ($foundUnRAR) {
    Write-Host "[FOUND] UnRAR.dll at: $foundUnRAR" -ForegroundColor Green
    
    # Copy to project
    $targetPath = "third_party\unrar"
    if (!(Test-Path $targetPath)) {
        New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
    }
    
    Copy-Item $foundUnRAR "$targetPath\UnRAR.dll" -Force
    Write-Host "✅ UnRAR.dll copied to: $targetPath\UnRAR.dll" -ForegroundColor Green
    
    # Copy to build directory
    if (Test-Path "build") {
        Copy-Item $foundUnRAR "build\UnRAR.dll" -Force
        Write-Host "✅ UnRAR.dll copied to: build\UnRAR.dll" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🎉 SUCCESS! UnRAR.dll is ready for bit7z" -ForegroundColor Green
    Write-Host "   Now bit7z can support RAR4 and RAR5 formats!" -ForegroundColor Green
    
} else {
    Write-Host "[INFO] WinRAR not found. Trying to download UnRAR.dll..." -ForegroundColor Yellow
    Write-Host ""
    
    # Download UnRAR.dll from official source
    $url = "https://www.win-rar.com/fileadmin/winrar-versions/unrardll/UnRARDLL.exe"
    $tempFile = "$env:TEMP\UnRARDLL.exe"
    
    try {
        Write-Host "[STEP 1] Downloading UnRAR.dll package..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
        Write-Host "✅ Downloaded: $tempFile" -ForegroundColor Green
        
        # Extract UnRAR.dll
        $extractPath = "$env:TEMP\unrar-extract"
        if (Test-Path $extractPath) {
            Remove-Item -Recurse -Force $extractPath
        }
        New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
        
        Write-Host "[STEP 2] Extracting UnRAR.dll..." -ForegroundColor Yellow
        
        # Use 7za.exe to extract
        $sevenZaPath = "third_party\7z-extra\x64\7za.exe"
        if (Test-Path $sevenZaPath) {
            & $sevenZaPath x $tempFile -o"$extractPath" -y
            
            # Find UnRAR.dll
            $unrarDll = Get-ChildItem -Path $extractPath -Name "UnRAR.dll" -Recurse | Select-Object -First 1
            if ($unrarDll) {
                $sourceDll = Join-Path $extractPath $unrarDll
                
                # Copy to project
                $targetPath = "third_party\unrar"
                if (!(Test-Path $targetPath)) {
                    New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                }
                
                Copy-Item $sourceDll "$targetPath\UnRAR.dll" -Force
                Write-Host "✅ UnRAR.dll extracted to: $targetPath\UnRAR.dll" -ForegroundColor Green
                
                # Copy to build directory
                if (Test-Path "build") {
                    Copy-Item $sourceDll "build\UnRAR.dll" -Force
                    Write-Host "✅ UnRAR.dll copied to: build\UnRAR.dll" -ForegroundColor Green
                }
                
                Write-Host ""
                Write-Host "🎉 SUCCESS! UnRAR.dll downloaded and ready!" -ForegroundColor Green
                
                # Cleanup
                Remove-Item -Recurse -Force $extractPath -ErrorAction SilentlyContinue
                
            } else {
                Write-Host "❌ UnRAR.dll not found in package" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ 7za.exe not found for extraction" -ForegroundColor Red
        }
        
        # Cleanup
        Remove-Item $tempFile -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Manual download instructions:" -ForegroundColor Yellow
        Write-Host "1. Go to: https://www.win-rar.com/download.html" -ForegroundColor White
        Write-Host "2. Download: UnRAR.dll" -ForegroundColor White
        Write-Host "3. Extract UnRAR.dll to: third_party\unrar\" -ForegroundColor White
        Write-Host "4. Copy to: build\" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Update BandiZip code to use UnRAR.dll" -ForegroundColor White
Write-Host "2. Rebuild: cmake --build build --config Debug" -ForegroundColor White
Write-Host "3. Test RAR5 files - should work now!" -ForegroundColor White
Write-Host ""

Write-Host "UnRAR.dll provides:" -ForegroundColor Cyan
Write-Host "✅ RAR4 support" -ForegroundColor Green
Write-Host "✅ RAR5 support" -ForegroundColor Green
Write-Host "✅ Encrypted RAR support" -ForegroundColor Green
Write-Host "✅ All RAR variants" -ForegroundColor Green
Write-Host ""

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

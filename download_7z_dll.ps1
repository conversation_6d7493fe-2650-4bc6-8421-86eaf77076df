# PowerShell script to download 7z.dll for bit7z support
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     Download 7z.dll for BandiZip" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 7-Zip Extra download URL (contains 7z.dll)
$url = "https://www.7-zip.org/a/7z2409-extra.7z"
$tempFile = "$env:TEMP\7z-extra.7z"
$extractPath = "$env:TEMP\7z-extra"
$targetPath = "third_party\7zip"

Write-Host "[STEP 1] Downloading 7-Zip Extra package..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $url -OutFile $tempFile -UseBasicParsing
    Write-Host "✅ Downloaded successfully: $tempFile" -ForegroundColor Green
} catch {
    Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download instructions:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://www.7-zip.org/download.html" -ForegroundColor White
    Write-Host "2. Download: '7-Zip Extra: standalone console version, 7z DLL'" -ForegroundColor White
    Write-Host "3. Extract 7z.dll to: $targetPath" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "[STEP 2] Checking for extraction tool..." -ForegroundColor Yellow

# Try to use existing 7za.exe to extract
$sevenZaPath = "third_party\7z-extra\x64\7za.exe"
if (Test-Path $sevenZaPath) {
    Write-Host "✅ Found 7za.exe: $sevenZaPath" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "[STEP 3] Extracting 7z.dll..." -ForegroundColor Yellow
    
    # Create extraction directory
    if (Test-Path $extractPath) {
        Remove-Item -Recurse -Force $extractPath
    }
    New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
    
    # Extract using 7za.exe
    $extractCmd = "& `"$sevenZaPath`" x `"$tempFile`" -o`"$extractPath`" -y"
    Invoke-Expression $extractCmd
    
    # Find 7z.dll in extracted files
    $dllPath = Get-ChildItem -Path $extractPath -Name "7z.dll" -Recurse | Select-Object -First 1
    if ($dllPath) {
        $sourceDll = Join-Path $extractPath $dllPath
        
        # Create target directory
        if (!(Test-Path $targetPath)) {
            New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
        }
        
        # Copy 7z.dll
        $targetDll = Join-Path $targetPath "7z.dll"
        Copy-Item $sourceDll $targetDll -Force
        
        Write-Host "✅ 7z.dll copied to: $targetDll" -ForegroundColor Green
        
        # Also copy to build directory
        $buildDll = "build\7z.dll"
        if (Test-Path "build") {
            Copy-Item $sourceDll $buildDll -Force
            Write-Host "✅ 7z.dll copied to: $buildDll" -ForegroundColor Green
        }
        
        # Cleanup
        Remove-Item -Recurse -Force $extractPath -ErrorAction SilentlyContinue
        Remove-Item $tempFile -ErrorAction SilentlyContinue
        
        Write-Host ""
        Write-Host "🎉 SUCCESS! 7z.dll is now available for bit7z" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Rebuild your project: cmake --build build --config Debug" -ForegroundColor White
        Write-Host "2. Run BandiZip - bit7z should now support all formats!" -ForegroundColor White
        
    } else {
        Write-Host "❌ 7z.dll not found in extracted files" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ 7za.exe not found at: $sevenZaPath" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual extraction needed:" -ForegroundColor Yellow
    Write-Host "1. Extract the downloaded file: $tempFile" -ForegroundColor White
    Write-Host "2. Find 7z.dll in the extracted files" -ForegroundColor White
    Write-Host "3. Copy it to: $targetPath\7z.dll" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

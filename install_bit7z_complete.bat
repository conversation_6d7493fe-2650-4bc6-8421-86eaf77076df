@echo off
echo.
echo ========================================
echo     Complete bit7z Installation
echo ========================================
echo.

echo [INFO] This script will install bit7z library completely
echo.

REM Check if vcpkg is available
where vcpkg >nul 2>&1
if %errorlevel% neq 0 (
    echo [STEP 1] Installing vcpkg...
    echo.
    
    REM Check if vcpkg directory already exists
    if exist "C:\vcpkg" (
        echo [INFO] vcpkg directory exists, updating...
        cd /d C:\vcpkg
        git pull
    ) else (
        echo [INFO] Cloning vcpkg...
        cd /d C:\
        git clone https://github.com/Microsoft/vcpkg.git
        cd vcpkg
    )
    
    echo [INFO] Bootstrapping vcpkg...
    .\bootstrap-vcpkg.bat
    
    echo [INFO] Adding vcpkg to PATH...
    setx PATH "%PATH%;C:\vcpkg" /M
    set PATH=%PATH%;C:\vcpkg
    
    echo [OK] vcpkg installed
) else (
    echo [OK] vcpkg already available
)

echo.
echo [STEP 2] Installing bit7z via vcpkg...
vcpkg install bit7z --triplet x64-windows
if %errorlevel% neq 0 (
    echo [ERROR] bit7z installation failed
    echo.
    echo Trying alternative installation...
    vcpkg install bit7z:x64-windows
    if %errorlevel% neq 0 (
        echo [ERROR] Alternative installation also failed
        goto :manual
    )
)

echo [OK] bit7z installed successfully
echo.

echo [STEP 3] Copying files to project...

REM Find vcpkg installation
set VCPKG_ROOT=C:\vcpkg
if not exist "%VCPKG_ROOT%" (
    for /f "tokens=*" %%i in ('where vcpkg 2^>nul') do (
        for %%j in ("%%i") do set VCPKG_ROOT=%%~dpj
    )
)

set VCPKG_INSTALLED=%VCPKG_ROOT%\installed\x64-windows

echo [INFO] vcpkg root: %VCPKG_ROOT%
echo [INFO] vcpkg installed: %VCPKG_INSTALLED%

REM Copy headers
if exist "%VCPKG_INSTALLED%\include\bit7z" (
    echo [COPY] Copying headers...
    xcopy "%VCPKG_INSTALLED%\include\bit7z" "third_party\bit7z\include\bit7z\" /E /I /Y
    echo [OK] Headers copied
) else (
    echo [ERROR] Headers not found at %VCPKG_INSTALLED%\include\bit7z
    goto :manual
)

REM Copy library files
if exist "%VCPKG_INSTALLED%\lib\bit7z.lib" (
    echo [COPY] Copying library...
    if not exist "third_party\bit7z\lib" mkdir "third_party\bit7z\lib"
    copy "%VCPKG_INSTALLED%\lib\bit7z.lib" "third_party\bit7z\lib\"
    echo [OK] Library copied
) else (
    echo [ERROR] Library not found at %VCPKG_INSTALLED%\lib\bit7z.lib
    goto :manual
)

REM Copy DLL if exists
if exist "%VCPKG_INSTALLED%\bin\bit7z.dll" (
    echo [COPY] Copying DLL...
    copy "%VCPKG_INSTALLED%\bin\bit7z.dll" "third_party\bit7z\lib\"
    echo [OK] DLL copied
)

REM Copy debug library if exists
if exist "%VCPKG_INSTALLED%\debug\lib\bit7z.lib" (
    echo [COPY] Copying debug library...
    copy "%VCPKG_INSTALLED%\debug\lib\bit7z.lib" "third_party\bit7z\lib\bit7z_d.lib"
    echo [OK] Debug library copied
)

goto :verify

:manual
echo.
echo ========================================
echo     Manual Installation Required
echo ========================================
echo.
echo vcpkg installation failed. Please try manual installation:
echo.
echo Option 1: Download precompiled bit7z
echo   1. Visit: https://github.com/rikyoz/bit7z/releases
echo   2. Download Windows x64 version
echo   3. Extract and copy:
echo      - include/bit7z/* to third_party/bit7z/include/bit7z/
echo      - lib/bit7z.lib to third_party/bit7z/lib/
echo.
echo Option 2: Compile from source
echo   1. Download source from GitHub
echo   2. Use CMake to compile
echo   3. Copy output files to third_party/bit7z/
echo.
goto :end

:verify
echo.
echo [VERIFY] Checking installation...
echo.

if exist "third_party\bit7z\include\bit7z\bit7zlibrary.hpp" (
    echo [OK] Headers: Available
) else (
    echo [ERROR] Headers: Missing
)

if exist "third_party\bit7z\lib\bit7z.lib" (
    echo [OK] Library: Available
    dir "third_party\bit7z\lib\bit7z.lib" | findstr /C:"bit7z.lib"
) else (
    echo [ERROR] Library: Missing
)

if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll: Available
) else (
    echo [ERROR] 7z.dll: Missing
)

echo.
if exist "third_party\bit7z\lib\bit7z.lib" if exist "third_party\7zip\7z.dll" (
    echo [SUCCESS] Complete bit7z installation ready!
    echo.
    echo Next steps:
    echo 1. Open Qt Creator
    echo 2. Open CMakeLists.txt
    echo 3. Configure and build
    echo.
    echo You now have high-performance bit7z compression!
) else (
    echo [INCOMPLETE] Installation not complete
    echo Please check missing files above
)

:end
echo.
pause

# 🚀 Qt5版本安装和启动指南

## 为什么选择Qt5？

✅ **更容易安装** - Qt5安装包更小，依赖更少  
✅ **更好的兼容性** - 支持更多Windows版本  
✅ **更稳定** - Qt5.15.2是LTS长期支持版本  
✅ **更快的构建** - 编译速度更快  

## 📥 Qt5安装步骤

### 方案1：Qt官方安装器（推荐）

1. **下载Qt5安装器**
   ```
   访问：https://www.qt.io/download-qt-installer
   下载：qt-unified-windows-x64-online.exe
   ```

2. **安装Qt5.15.2组件**
   - 创建Qt账户（免费）
   - 选择开源版本
   - 在组件选择中找到：
     - ✅ Qt 5.15.2
     - ✅ MinGW 8.1.0 64-bit 编译器
     - ✅ Qt Creator IDE
     - ✅ CMake 工具

3. **验证安装**
   ```cmd
   # 打开新的命令提示符
   qmake --version
   # 应该显示：Qt version 5.15.2
   
   gcc --version
   # 应该显示MinGW版本信息
   ```

### 方案2：离线安装包

1. **下载Qt5.15.2离线安装包**
   ```
   访问：https://download.qt.io/archive/qt/5.15/5.15.2/
   下载：qt-opensource-windows-x86-5.15.2.exe
   ```

2. **安装时选择组件**
   - Qt 5.15.2 → MinGW 8.1.0 64-bit
   - Qt 5.15.2 → Sources
   - Developer and Designer Tools → MinGW 8.1.0 64-bit
   - Developer and Designer Tools → CMake

### 方案3：使用包管理器

#### 使用Chocolatey
```powershell
# 安装Chocolatey（如果没有）
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# 安装Qt5
choco install qt5-default
```

#### 使用vcpkg
```cmd
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg install qt5[core,widgets] --triplet x64-windows
```

## 🏃‍♂️ 启动项目

### 使用PowerShell脚本（推荐）
```powershell
# 运行Qt5启动脚本
powershell -ExecutionPolicy Bypass -File start_qt5.ps1
```

### 使用Qt Creator
1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择项目根目录的 `CMakeLists.txt`
4. 选择Qt5.15.2 MinGW套件
5. 点击运行按钮

### 手动命令行构建
```cmd
# 设置Qt5路径
set CMAKE_PREFIX_PATH=C:\Qt\5.15.2\mingw81_64

# 构建项目
cmake -B build -S . -G "MinGW Makefiles"
cmake --build build --config Release

# 运行程序
.\build\BandiZip.exe
```

## 🎯 预期效果

程序启动后您将看到：

```
┌─────────────────────────────────────────────────────────────────┐
│ 📦 好压万能压缩                    立即登录  立即开通  ─  □  ✕ │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│    │     📂      │  │     📁      │  │     🎬      │           │
│    │             │  │             │  │             │           │
│    │    解压     │  │    压缩     │  │  视频压缩   │           │
│    │点击/拖入压缩│  │点击/拖入文件│  │支持MP4、WMV │           │
│    │包，一键快速 │  │，支持快速压│  │、AVI等多种  │           │
│    │解压         │  │缩为Zip、7z  │  │格式         │           │
│    └─────────────┘  └─────────────┘  └─────────────┘           │
│    ┌─────────────┐  ┌─────────────┐                            │
│    │     🖼️      │  │     📄      │                            │
│    │             │  │             │                            │
│    │  图片压缩   │  │  PDF压缩    │                            │
│    │支持JPG、PNG │  │高效压缩，保 │                            │
│    │、GIF、BMP等 │  │持文档质量   │                            │
│    │多种格式     │  │             │                            │
│    └─────────────┘  └─────────────┘                            │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Qt5版本优势

### 兼容性优势
- ✅ 支持Windows 7/8/10/11
- ✅ 更少的系统依赖
- ✅ 更好的向后兼容性

### 开发优势
- ✅ 更快的编译速度
- ✅ 更小的安装包
- ✅ 更稳定的API
- ✅ 丰富的文档和示例

### 功能完整性
- ✅ 所有核心功能完全支持
- ✅ 现代化UI设计
- ✅ 完整的拖拽支持
- ✅ 多线程压缩处理

## 🆘 常见问题

### Q: 提示"找不到Qt5"
**A:** 确保Qt5正确安装并设置环境变量
```cmd
set CMAKE_PREFIX_PATH=C:\Qt\5.15.2\mingw81_64
```

### Q: 编译错误"cannot find -lQt5Core"
**A:** 检查Qt5路径和编译器匹配
```cmd
# 确保使用正确的MinGW版本
where gcc
# 应该指向Qt安装目录下的MinGW
```

### Q: 程序启动后界面异常
**A:** 确保Qt5 DLL文件可访问
```cmd
# 将Qt5的bin目录添加到PATH
set PATH=C:\Qt\5.15.2\mingw81_64\bin;%PATH%
```

### Q: 7-Zip功能不工作
**A:** 安装7-Zip到默认路径
```
下载：https://www.7-zip.org/download.html
安装到：C:\Program Files\7-Zip\
```

## 📊 Qt5 vs Qt6 对比

| 特性 | Qt5.15.2 | Qt6.x |
|------|----------|-------|
| 安装大小 | ~2GB | ~4GB |
| 编译速度 | 快 | 较慢 |
| 兼容性 | 优秀 | 良好 |
| 稳定性 | 非常稳定 | 稳定 |
| 学习资源 | 丰富 | 较少 |
| 长期支持 | 2023年底 | 活跃开发 |

## 🎉 成功标志

如果看到以下内容，说明Qt5项目启动成功：
- ✅ 现代化的卡片式界面
- ✅ 5个功能卡片（解压、压缩、视频、图片、PDF）
- ✅ 浅蓝色渐变背景
- ✅ 流畅的悬停动画效果
- ✅ 完整的拖拽功能

## 📞 技术支持

如果遇到问题：
1. 检查Qt5版本是否为5.15.2
2. 确认MinGW编译器正确安装
3. 验证CMAKE_PREFIX_PATH设置
4. 使用Qt Creator可以避免大部分配置问题

---

**Qt5版本 - 更稳定、更兼容、更容易上手！** 🚀

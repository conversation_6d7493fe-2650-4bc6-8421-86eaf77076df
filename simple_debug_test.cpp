// 将这段代码添加到 main.cpp 的 main 函数开头进行测试

#ifdef HAVE_BIT7Z
#include <bit7z/bit7zlibrary.hpp>
#include <bit7z/bitfileextractor.hpp>
#include <bit7z/bitformat.hpp>
using namespace bit7z;
#endif

void testBit7zQuick() {
    qDebug() << "=== Quick bit7z Test ===";
    
#ifdef HAVE_BIT7Z
    qDebug() << "✅ HAVE_BIT7Z defined";
    
    QStringList testPaths = {
        QApplication::applicationDirPath() + "/7z.dll",
        "third_party/7zip/7z.dll"
    };
    
    for (const QString& path : testPaths) {
        qDebug() << "Testing:" << path;
        if (QFileInfo::exists(path)) {
            qDebug() << "  File exists, size:" << QFileInfo(path).size();
            try {
                Bit7zLibrary lib(path.toStdString());
                qDebug() << "  ✅ bit7z init SUCCESS!";
                
                BitFileExtractor extractor(lib);
                qDebug() << "  ✅ extractor creation SUCCESS!";
                return;
            } catch (const std::exception& e) {
                qDebug() << "  ❌ Error:" << e.what();
            }
        } else {
            qDebug() << "  File not found";
        }
    }
    qDebug() << "❌ bit7z test failed";
#else
    qDebug() << "❌ HAVE_BIT7Z not defined";
#endif
}

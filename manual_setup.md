# 🔧 手动集成7-Zip和bit7z依赖

如果自动下载脚本无法工作，可以按照以下步骤手动集成依赖库。

## 📁 目录结构

确保项目有以下目录结构：
```
BandiZip/
├── third_party/
│   ├── 7zip/
│   │   └── 7z.dll          # 7-Zip核心库
│   └── bit7z/
│       ├── include/
│       │   └── bit7z/
│       │       └── bit7z.hpp
│       └── lib/
│           ├── bit7z.lib    # 静态库
│           └── bit7z.dll    # 动态库（可选）
├── src/
├── resources/
└── CMakeLists.txt
```

## 📥 手动下载步骤

### 1. 下载7-Zip DLL

#### 方法1：从官网下载
1. 访问：https://www.7-zip.org/download.html
2. 下载 "7-Zip Extra: standalone console version"
3. 解压后找到 `7z.dll` 文件
4. 复制到 `third_party/7zip/7z.dll`

#### 方法2：从系统安装复制
```powershell
# 如果已安装7-Zip，直接复制
Copy-Item "C:\Program Files\7-Zip\7z.dll" "third_party\7zip\7z.dll"
```

### 2. 下载bit7z库

#### 方法1：预编译版本（推荐）
1. 访问：https://github.com/rikyoz/bit7z/releases
2. 下载最新的 `bit7z-*-windows-x64.zip`
3. 解压后复制文件：
   ```
   解压包/include/ → third_party/bit7z/include/
   解压包/lib/     → third_party/bit7z/lib/
   ```

#### 方法2：使用vcpkg
```powershell
# 安装vcpkg
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat

# 安装bit7z
.\vcpkg install bit7z --triplet x64-windows

# 复制文件
Copy-Item "vcpkg\installed\x64-windows\include\bit7z" "third_party\bit7z\include\" -Recurse
Copy-Item "vcpkg\installed\x64-windows\lib\bit7z.lib" "third_party\bit7z\lib\"
```

#### 方法3：从源码编译
```powershell
# 克隆源码
git clone https://github.com/rikyoz/bit7z.git
cd bit7z

# 使用CMake编译
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=..\..\third_party\bit7z
cmake --build . --config Release
cmake --install .
```

## 🔍 验证集成

运行以下PowerShell命令验证文件是否正确放置：

```powershell
# 检查7z.dll
if (Test-Path "third_party\7zip\7z.dll") {
    Write-Host "✅ 7z.dll 已集成" -ForegroundColor Green
    Get-Item "third_party\7zip\7z.dll" | Select-Object Name, Length, LastWriteTime
} else {
    Write-Host "❌ 7z.dll 缺失" -ForegroundColor Red
}

# 检查bit7z头文件
if (Test-Path "third_party\bit7z\include\bit7z\bit7z.hpp") {
    Write-Host "✅ bit7z头文件已集成" -ForegroundColor Green
} else {
    Write-Host "❌ bit7z头文件缺失" -ForegroundColor Red
}

# 检查bit7z库文件
if (Test-Path "third_party\bit7z\lib\bit7z.lib") {
    Write-Host "✅ bit7z库文件已集成" -ForegroundColor Green
    Get-Item "third_party\bit7z\lib\bit7z.lib" | Select-Object Name, Length, LastWriteTime
} else {
    Write-Host "❌ bit7z库文件缺失" -ForegroundColor Red
}
```

## 📋 文件清单

### 必需文件
- `third_party/7zip/7z.dll` - 7-Zip核心库（约1.5MB）
- `third_party/bit7z/include/bit7z/bit7z.hpp` - bit7z主头文件
- `third_party/bit7z/lib/bit7z.lib` - bit7z静态库

### 可选文件
- `third_party/bit7z/lib/bit7z.dll` - bit7z动态库
- `third_party/bit7z/include/bit7z/*.hpp` - 其他头文件

## 🔧 更新CMake配置

集成完依赖文件后，运行：

```powershell
# 更新CMake配置以使用项目内依赖
.\update_cmake.ps1

# 清理旧的构建文件
Remove-Item -Recurse -Force build -ErrorAction SilentlyContinue

# 重新构建项目
.\build_qt6.bat
```

## 📦 部署优势

集成依赖库后的优势：

### ✅ 独立部署
- 不依赖系统安装的7-Zip
- 不需要额外安装bit7z
- 可以直接分发exe和dll文件

### ✅ 版本控制
- 确保所有环境使用相同版本
- 避免版本冲突问题
- 便于团队协作

### ✅ 性能优化
- 使用最新版本的压缩库
- bit7z提供更好的C++接口
- 7z.dll作为稳定的回退方案

## 🚀 构建输出

成功集成后，构建输出目录将包含：

```
build/
├── BandiZip.exe           # 主程序
├── 7z.dll                # 7-Zip库（自动复制）
├── bit7z.dll              # bit7z库（如果存在）
├── Qt6Core.dll            # Qt6核心库
├── Qt6Gui.dll             # Qt6 GUI库
├── Qt6Widgets.dll         # Qt6控件库
└── plugins/
    └── platforms/
        └── qwindows.dll   # Qt6平台插件
```

这样的输出可以直接分发，无需额外安装任何依赖！

## 🆘 故障排除

### 问题1：7z.dll版本不兼容
**解决方案：** 确保下载的是64位版本的7z.dll

### 问题2：bit7z编译错误
**解决方案：** 检查头文件路径，确保bit7z.hpp在正确位置

### 问题3：运行时找不到DLL
**解决方案：** 检查CMake是否正确复制了所有DLL文件

### 问题4：链接错误
**解决方案：** 确保bit7z.lib是为相同的编译器和架构编译的

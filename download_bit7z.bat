@echo off
echo.
echo ========================================
echo     Download bit7z Library
echo ========================================
echo.

REM Check if bit7z already exists
if exist "third_party\bit7z\lib\bit7z.lib" (
    echo [OK] bit7z library already exists
    echo If you want to re-download, delete the lib folder first
    goto :verify
)

echo [INFO] bit7z library not found, need to download
echo.
echo Since you have 7z.dll, you have several options:
echo.
echo Option 1: Use vcpkg (Recommended)
echo   vcpkg install bit7z --triplet x64-windows
echo.
echo Option 2: Download from GitHub
echo   Visit: https://github.com/rikyoz/bit7z/releases
echo   Download the latest Windows x64 release
echo   Extract to third_party\bit7z\
echo.
echo Option 3: Continue without bit7z
echo   The project will work fine using 7z.dll fallback mode
echo.

set /p choice="Choose option (1/2/3): "

if "%choice%"=="1" goto :vcpkg
if "%choice%"=="2" goto :manual
if "%choice%"=="3" goto :fallback
goto :end

:vcpkg
echo.
echo [VCPKG] Installing bit7z via vcpkg...
echo.
where vcpkg >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] vcpkg not found in PATH
    echo Please install vcpkg first:
    echo   git clone https://github.com/Microsoft/vcpkg.git
    echo   cd vcpkg
    echo   .\bootstrap-vcpkg.bat
    goto :end
)

echo Installing bit7z...
vcpkg install bit7z --triplet x64-windows
if %errorlevel% neq 0 (
    echo [ERROR] vcpkg installation failed
    goto :end
)

echo Copying files from vcpkg...
if exist "C:\vcpkg\installed\x64-windows\include\bit7z" (
    xcopy "C:\vcpkg\installed\x64-windows\include\bit7z" "third_party\bit7z\include\bit7z\" /E /I /Y
    echo [OK] Headers copied
)

if exist "C:\vcpkg\installed\x64-windows\lib\bit7z.lib" (
    copy "C:\vcpkg\installed\x64-windows\lib\bit7z.lib" "third_party\bit7z\lib\"
    echo [OK] Library copied
)

goto :verify

:manual
echo.
echo [MANUAL] Manual download instructions:
echo.
echo 1. Open browser and go to:
echo    https://github.com/rikyoz/bit7z/releases
echo.
echo 2. Download the latest release (look for Windows x64 version)
echo.
echo 3. Extract the downloaded file
echo.
echo 4. Copy files to project:
echo    - Copy include\bit7z\* to third_party\bit7z\include\bit7z\
echo    - Copy lib\bit7z.lib to third_party\bit7z\lib\
echo.
echo 5. Run this script again to verify
echo.
pause
goto :end

:fallback
echo.
echo [FALLBACK] Continuing without bit7z
echo.
echo The project will use 7z.dll fallback mode:
echo - Fully functional compression/decompression
echo - Compatible with all 7-Zip formats
echo - Slightly lower performance than bit7z
echo - No real-time progress callbacks
echo.
echo This is perfectly fine for most use cases!
goto :verify

:verify
echo.
echo [VERIFY] Checking integration status...
echo.

if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll: Available
) else (
    echo [ERROR] 7z.dll: Missing
)

if exist "third_party\bit7z\include\bit7z\bit7z.hpp" (
    echo [OK] bit7z headers: Available
    set BIT7Z_HEADERS=1
) else (
    echo [INFO] bit7z headers: Not available
    set BIT7Z_HEADERS=0
)

if exist "third_party\bit7z\lib\bit7z.lib" (
    echo [OK] bit7z library: Available
    set BIT7Z_LIB=1
) else (
    echo [INFO] bit7z library: Not available
    set BIT7Z_LIB=0
)

echo.
echo ========================================
echo     Integration Summary
echo ========================================
echo.

if exist "third_party\7zip\7z.dll" (
    if %BIT7Z_HEADERS%==1 if %BIT7Z_LIB%==1 (
        echo Status: HIGH PERFORMANCE MODE
        echo - bit7z C++ interface: Enabled
        echo - 7z.dll compression: Enabled
        echo - Real-time progress: Enabled
        echo - Exception handling: Enabled
    ) else (
        echo Status: COMPATIBILITY MODE
        echo - 7z.dll compression: Enabled
        echo - Command-line interface: Enabled
        echo - Basic progress: Enabled
        echo - Fully functional: Yes
    )
    
    echo.
    echo Next steps:
    echo 1. Update CMake: .\update_cmake.ps1
    echo 2. Clean build: Remove-Item -Recurse build -Force
    echo 3. Rebuild: .\build_qt6.bat
    
) else (
    echo Status: MISSING DEPENDENCIES
    echo Please ensure 7z.dll is in third_party\7zip\
)

:end
echo.
pause

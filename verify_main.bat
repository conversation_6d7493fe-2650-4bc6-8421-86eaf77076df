@echo off
echo Verifying main.cpp fix...
echo.

REM Check if main.cpp contains MainWindow
findstr /C:"MainWindow" main.cpp >nul
if %errorlevel% equ 0 (
    echo [OK] main.cpp now includes MainWindow
) else (
    echo [ERROR] main.cpp still missing MainWindow
    goto :end
)

REM Check if main.cpp contains the correct application name
findstr /C:"好压万能压缩" main.cpp >nul
if %errorlevel% equ 0 (
    echo [OK] Application name set correctly
) else (
    echo [WARNING] Application name not found
)

REM Check if Hello World code is removed
findstr /C:"Hello world" main.cpp >nul
if %errorlevel% neq 0 (
    echo [OK] Hello World code removed
) else (
    echo [ERROR] Hello World code still present
    goto :end
)

REM Check if QPushButton is removed from main.cpp
findstr /C:"QPushButton" main.cpp >nul
if %errorlevel% neq 0 (
    echo [OK] QPushButton removed from main.cpp
) else (
    echo [ERROR] QPushButton still in main.cpp
    goto :end
)

echo.
echo [SUCCESS] main.cpp has been fixed!
echo.
echo The application will now show:
echo - Modern card-based UI
echo - 5 feature cards (Extract, Compress, Video, Image, PDF)
echo - Custom title bar with window controls
echo - Light blue gradient theme
echo.
echo Next steps:
echo 1. Clean and rebuild the project in Qt Creator
echo 2. Or run: .\build_qt6.bat
echo 3. The correct BandiZip window should now appear

:end
echo.
pause

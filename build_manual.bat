@echo off
echo.
echo ========================================
echo     Manual Build Script
echo ========================================
echo.

REM Set Qt6 path (adjust if needed)
set QT6_PATH=D:\Qt\6.9.0\mingw_64
set CMAKE_PREFIX_PATH=%QT6_PATH%

echo [INFO] Using Qt6 path: %QT6_PATH%
echo [INFO] CMAKE_PREFIX_PATH: %CMAKE_PREFIX_PATH%

REM Check if Qt6 exists
if not exist "%QT6_PATH%\bin\qmake.exe" (
    echo [ERROR] Qt6 not found at: %QT6_PATH%
    echo Please adjust QT6_PATH in this script
    pause
    exit /b 1
)

echo [OK] Qt6 found

REM Clean any existing build
if exist build-manual rmdir /s /q build-manual 2>nul
mkdir build-manual

echo.
echo [STEP 1] Running CMake configure...
cd build-manual

cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%"

if %errorlevel% neq 0 (
    echo [ERROR] CMake configure failed
    cd ..
    pause
    exit /b 1
)

echo [OK] CMake configure successful

echo.
echo [STEP 2] Building project...
cmake --build . --config Debug

if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    cd ..
    pause
    exit /b 1
)

echo [OK] Build successful

cd ..

echo.
echo [SUCCESS] Manual build complete!
echo.
echo Executable location: build-manual\BandiZip.exe
echo.
echo To run the program:
echo   cd build-manual
echo   .\BandiZip.exe
echo.
pause

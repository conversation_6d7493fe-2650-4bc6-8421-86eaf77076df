# 简单的RAR解压工具
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     简单RAR解压工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$rarFile = "C:/Users/<USER>/Desktop/压缩文件测试/Ai.rar"
$outputDir = "C:/Users/<USER>/Desktop/压缩文件测试/extracted"

Write-Host "RAR文件: $rarFile" -ForegroundColor Green
Write-Host "输出目录: $outputDir" -ForegroundColor Green
Write-Host ""

# 检查文件是否存在
if (-not (Test-Path $rarFile)) {
    Write-Host "❌ RAR文件不存在" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    Write-Host "✅ 创建输出目录" -ForegroundColor Green
}

$success = $false

# 尝试使用7-Zip
$sevenZipPath = "C:\Program Files\7-Zip\7z.exe"
if (Test-Path $sevenZipPath) {
    Write-Host "🔧 找到7-Zip，开始解压..." -ForegroundColor Blue
    try {
        $arguments = @("x", "-y", "-o`"$outputDir`"", "`"$rarFile`"")
        $process = Start-Process -FilePath $sevenZipPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        
        Write-Host "7-Zip退出代码: $($process.ExitCode)" -ForegroundColor Blue
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ 7-Zip解压成功!" -ForegroundColor Green
            $success = $true
        } else {
            Write-Host "❌ 7-Zip解压失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 7-Zip解压出错: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ 未找到7-Zip" -ForegroundColor Yellow
}

# 尝试使用WinRAR
if (-not $success) {
    $winrarPath = "C:\Program Files\WinRAR\WinRAR.exe"
    if (Test-Path $winrarPath) {
        Write-Host "🔧 找到WinRAR，开始解压..." -ForegroundColor Blue
        try {
            $arguments = @("x", "-y", "`"$rarFile`"", "`"$outputDir`"")
            $process = Start-Process -FilePath $winrarPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
            
            Write-Host "WinRAR退出代码: $($process.ExitCode)" -ForegroundColor Blue
            
            if ($process.ExitCode -eq 0) {
                Write-Host "✅ WinRAR解压成功!" -ForegroundColor Green
                $success = $true
            } else {
                Write-Host "❌ WinRAR解压失败" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ WinRAR解压出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ 未找到WinRAR" -ForegroundColor Yellow
    }
}

Write-Host ""
if ($success) {
    Write-Host "🎉 RAR文件解压完成!" -ForegroundColor Green
    Write-Host "📁 解压到: $outputDir" -ForegroundColor Green
    
    # 显示解压的文件
    if (Test-Path $outputDir) {
        $extractedFiles = Get-ChildItem -Path $outputDir -Recurse
        Write-Host ""
        Write-Host "解压的文件:" -ForegroundColor Cyan
        foreach ($file in $extractedFiles) {
            if ($file.PSIsContainer) {
                Write-Host "  📁 $($file.Name)" -ForegroundColor Blue
            } else {
                $sizeKB = [math]::Round($file.Length/1KB, 2)
                Write-Host "  📄 $($file.Name) ($sizeKB KB)" -ForegroundColor White
            }
        }
    }
} else {
    Write-Host "❌ 解压失败" -ForegroundColor Red
    Write-Host ""
    Write-Host "建议解决方案:" -ForegroundColor Yellow
    Write-Host "1. 安装7-Zip: https://www.7-zip.org/download.html" -ForegroundColor White
    Write-Host "2. 安装WinRAR: https://www.win-rar.com/download.html" -ForegroundColor White
    Write-Host "3. 使用在线RAR解压工具" -ForegroundColor White
}

Write-Host ""
Write-Host "完成。" -ForegroundColor Gray

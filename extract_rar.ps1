# Simple RAR extractor
Write-Host "========================================"
Write-Host "     Simple RAR Extractor"
Write-Host "========================================"

$rarFile = "C:/Users/<USER>/Desktop/压缩文件测试/Ai.rar"
$outputDir = "C:/Users/<USER>/Desktop/压缩文件测试/extracted"

Write-Host "RAR file: $rarFile"
Write-Host "Output dir: $outputDir"
Write-Host ""

# Check if file exists
if (-not (Test-Path $rarFile)) {
    Write-Host "ERROR: RAR file not found"
    exit 1
}

# Create output directory
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    Write-Host "Created output directory"
}

$success = $false

# Try 7-Zip
$sevenZipPath = "C:\Program Files\7-Zip\7z.exe"
if (Test-Path $sevenZipPath) {
    Write-Host "Found 7-Zip, extracting..."
    try {
        $arguments = @("x", "-y", "-o`"$outputDir`"", "`"$rarFile`"")
        $process = Start-Process -FilePath $sevenZipPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        
        Write-Host "7-Zip exit code: $($process.ExitCode)"
        
        if ($process.ExitCode -eq 0) {
            Write-Host "SUCCESS: 7-Zip extraction completed!"
            $success = $true
        } else {
            Write-Host "ERROR: 7-Zip extraction failed"
        }
    } catch {
        Write-Host "ERROR: 7-Zip exception: $($_.Exception.Message)"
    }
} else {
    Write-Host "7-Zip not found"
}

# Try WinRAR if 7-Zip failed
if (-not $success) {
    $winrarPath = "C:\Program Files\WinRAR\WinRAR.exe"
    if (Test-Path $winrarPath) {
        Write-Host "Found WinRAR, extracting..."
        try {
            $arguments = @("x", "-y", "`"$rarFile`"", "`"$outputDir`"")
            $process = Start-Process -FilePath $winrarPath -ArgumentList $arguments -Wait -PassThru -NoNewWindow
            
            Write-Host "WinRAR exit code: $($process.ExitCode)"
            
            if ($process.ExitCode -eq 0) {
                Write-Host "SUCCESS: WinRAR extraction completed!"
                $success = $true
            } else {
                Write-Host "ERROR: WinRAR extraction failed"
            }
        } catch {
            Write-Host "ERROR: WinRAR exception: $($_.Exception.Message)"
        }
    } else {
        Write-Host "WinRAR not found"
    }
}

Write-Host ""
if ($success) {
    Write-Host "RAR file extracted successfully!"
    Write-Host "Output location: $outputDir"
    
    # Show extracted files
    if (Test-Path $outputDir) {
        $extractedFiles = Get-ChildItem -Path $outputDir -Recurse
        Write-Host ""
        Write-Host "Extracted files:"
        foreach ($file in $extractedFiles) {
            if ($file.PSIsContainer) {
                Write-Host "  [DIR]  $($file.Name)"
            } else {
                $sizeKB = [math]::Round($file.Length/1KB, 2)
                Write-Host "  [FILE] $($file.Name) ($sizeKB KB)"
            }
        }
    }
} else {
    Write-Host "FAILED: Could not extract RAR file"
    Write-Host ""
    Write-Host "Solutions:"
    Write-Host "1. Install 7-Zip: https://www.7-zip.org/download.html"
    Write-Host "2. Install WinRAR: https://www.win-rar.com/download.html"
    Write-Host "3. Use online RAR extractor"
}

Write-Host ""
Write-Host "Done."

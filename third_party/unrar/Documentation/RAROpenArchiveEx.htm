<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>

<head>
<title>UnRAR.dll Manual</title>
</head>

<body>

<h3>HANDLE PASCAL RAROpenArchiveEx(struct RAROpenArchiveDataEx *ArchiveData)</h3>

<h3>Description</h3>
<p>Open RAR archive and allocate memory structures. Replaces the obsolete
<a href="RAROpenArchive.htm">RAROpenArchive</a> providing more options
and Unicode names support.
</p>

<h3>Parameters</h3>

<i>ArchiveData</i>
<blockquote>
Points to <a href="RAROpenArchiveDataEx.htm">RAROpenArchiveDataEx structure</a>.
</blockquote>

<h3>Return values</h3>
<blockquote>
Archive handle or NULL in case of error.
</blockquote>

<h3>See also</h3>
<blockquote>
  <a href="RAROpenArchiveDataEx.htm">RAROpenArchiveDataEx</a> structure.
</blockquote>


</body>

</html>

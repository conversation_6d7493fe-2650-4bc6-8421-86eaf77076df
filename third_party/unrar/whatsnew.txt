List of unrar.dll API changes. We do not include performance and reliability
improvements into this list, but this library and RAR/UnRAR tools share
the same source code. So the latest version of unrar.dll usually contains
same decompression algorithm changes as the latest UnRAR version.
============================================================================

--  5 November 2024

Minimum supported Windows version is Windows 7.


-- 15 March 2024

New MarkOfTheWeb field of RAROpenArchiveDataEx structure allows controls
the archive Mark of the Web propagation to extracted files.


-- 23 October 2023

New ERAR_LARGE_DICT error code is returned if dictionary size of extracted
file exceeds the dictionary size limit.

New callback event UCM_LARGEDICT is added. It allows to confirm or reject
extracting a file, which dictionary size exceeds the limit.

Fields ArcNameEx, ArcNameExSize, FileNameEx, FileNameExSize added to
struct RARHeaderDataEx allow to return file names exceeding the previous
1023 characters limit.

RAR_DLL_VERSION is incremented to 9.


-- 2 February 2019

New CmtBufW field of RAROpenArchiveDataEx structure allows to read
the archive comment in Unicode.


-- 10 January 2019

New OpFlags field and ROADOF_KEEPBROKEN flag in RAROpenArchiveDataEx structure
to specify if extracted files with invalid checksum shall be preserved.
Reserved[28] in the same structure is changed to Reserved[27].


--  3 October 2018

UnpVer field of RARHeaderData and RARHeaderDataEx structures erroneously
returned 200 instead of 50 for RAR 5.0 archives. Fixed now.


--  17 September 2016

New MtimeLow, MtimeHigh, CtimeLow, CtimeHigh, AtimeLow, AtimeHigh fields
of RARHeaderDataEx structure provide information about high precision times
of archived files.

RAR_DLL_VERSION is incremented to 8.


--  6 July 2015

New RedirType, RedirName, RedirNameSize, DirTarget fields of RARHeaderDataEx
structure provide information about file system redirection headers.

Please be sure to fill either the entire RARHeaderDataEx structure
or at least its Reserved field with zeroes before passing to RARReadHeaderEx.

RAR_DLL_VERSION is incremented to 7.


--  5 November 2013

OpenResult field of RAROpenArchiveData and RAROpenArchiveDataEx
structures can return the new ERAR_BAD_PASSWORD error code.
This code can be returned only for archives with encrypted headers
in RAR 5.0 format.


--  29 October 2013

RARProcessFile and RARProcessFileW can return the new ERAR_BAD_PASSWORD
error code. This code can be returned only for archives in RAR 5.0 format.


--  10 September 2013

RARProcessFile and RARProcessFileW can return two additional error codes:
ERAR_NO_MEMORY and ERAR_EREFERENCE. See appropriate help topics for details.


--  24 December 2012

1) Stricter requirements for return value of callback function.
You need to return -1 to cancel the operation, 1 if you processed an event
and want to continue and 0 for those events which you do not need to process
or do not know how to process including unknown event types.

Earlier versions of documentation told about "zero or positive value" to
continue, but it was too broad and did not allow unrar.dll to detect
if application actually processed the event.

2) RARHeaderDataEx structure stores the compression dictionary size
in kilobytes in DictSize field instead of Flags. RAR 5.0 dictionary
maximum size is too big to fit into previously used Flags bits. 

Old RARHeaderData structure does not provide the dictionary size at all.

3) RARHeaderDataEx structure contains new fields HashType and Hash fields,
which can be used to detect the type of file checksum and obtain BLAKE2
checksum for RAR_HASH_BLAKE2 HashType.

4) new RHDF_DIRECTORY (0x20) flag in RARHeaderData and RARHeaderDataEx
structures indicates the directory entry.


--  26 April 2012

New callback events UCM_CHANGEVOLUMEW and UCM_NEEDPASSWORDW are added.
You can use them to set Unicode name of next volume and Unicode password.

Also now it is recommended to return 0 in callback function for those event
types which you do not process including unknown event types.

Minimum supported Windows version is Windows XP.


--   1 March 2011

Minimum supported Windows version is Windows 2000.


--  24 November 2010

Fields Callback and UserData are added to RAROpenArchiveDataEx structure.
You can set them instead of calling RARSetCallback to define your callback
function. 

Using these two new fields, you can specify the callback function
at the same time as opening an archive and not after that, as it was in case
of RARSetCallback. It allows RAROpenArchiveEx to read the archive comment
in archive with encrypted headers. It could not be done in case of
RARSetCallback call, because a password was not available yet at the moment
of reading the comment. So specifying the callback function
in RAROpenArchiveDataEx is more preferable solution than RARSetCallback,
though RARSetCallback is supported by library too.

Space for these two new structure members was taken from Reserved array.
So it should not affect the compatibility provided that Reserved array
in RAROpenArchiveDataEx was set to zero as suggested in documentation.

Added the information in user manual that Reserved array in RARHeaderDataEx
structure must be filled with zeroes. While it is not important
in the current unrar.dll version, it may become important in the future.

Value returned by RARGetDllVersion and RAR_DLL_VERSION in unrar.h
are incremented to 5.

UnRAR.dll manual converted to HTML format and moved to Documentation folder.


--  18 January 2008

All LONG parameters of CallbackProc function were changed
to LPARAM type for 64 bit mode compatibility.


--  12 December 2007

Added new RAR_OM_LIST_INCSPLIT open mode for function RAROpenArchive.


--  14 August 2007

Added NoCrypt\unrar_nocrypt.dll without decryption code for those
applications where presence of encryption or decryption code is not
allowed because of legal restrictions.


--  14 December 2006

Added ERAR_MISSING_PASSWORD error type. This error is returned
if empty password is specified for encrypted file.


--  12 June 2003

Added RARProcessFileW function, Unicode version of RARProcessFile


--  9 August 2002

Added RAROpenArchiveEx function allowing to specify Unicode archive
name and get archive flags.


--  24 January 2002

Added RARReadHeaderEx function allowing to read Unicode file names
and 64 bit file sizes.


--  23 January 2002

Added ERAR_UNKNOWN error type (it is used for all errors which
do not have special ERAR code yet) and UCM_NEEDPASSWORD callback
message.

Unrar.dll automatically opens all next volumes not only when extracting,
but also in RAR_OM_LIST mode.


--  27 November 2001

RARSetChangeVolProc and RARSetProcessDataProc are replaced by
the single callback function installed with RARSetCallback.
Unlike old style callbacks, the new function accepts the user defined
parameter. Unrar.dll still supports RARSetChangeVolProc and
RARSetProcessDataProc for compatibility purposes, but if you write
a new application, better use RARSetCallback.

File comments support is not implemented in the new DLL version yet.
Now CmtState is always 0.


--  13 August 2001

Added RARGetDllVersion function, so you may distinguish old unrar.dll,
which used C style callback functions and the new one with PASCAL callbacks.


--  10 May 2001

Callback functions in RARSetChangeVolProc and RARSetProcessDataProc
use PASCAL style call convention now.

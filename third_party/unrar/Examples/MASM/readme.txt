Win32ASM unrar.dll example
==========================
Created using RadASM (www.radasm.com) IDE and MASM32(http://movsd.com).

Copy unrar.lib to directory for compilation.

The compiled unrar.exe file uses the first .rar file it finds.
-------------------------------------------------------------------------------
Get a full UNRAR (De)Installation System here: http://flo.mueckeimnetz.de/eis

Please drop a line if you find the samples or the include file useful - I'd be
glad to hear about.

-<PERSON><PERSON><PERSON> (devATmueckeimnetz.de)
[Project]
Assembler=masm
Type=Win32 App
Description=Win32ASM UnRAR example
Backup=$P\Bak\
Group=1
GroupExpand=1
Res.rc=1
[Files]
1=unrar.Asm
2=unrar.Inc
3=
4=rsrc.RC
5=
[MakeFiles]
0=unrar.rap
1=rsrc.rc
2=unrar.asm
3=unrar.obj
4=rsrc.res
5=unrar.exe
6=unrar.def
7=unrar.dll
8=unrar.txt
9=unrar.lib
10=unrar.mak
11=unrar.hla
[MakeDef]
Menu=1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0
1=4,O,$B\RC.EXE /v,1
2=3,O,$B\ML.EXE /c /coff /Cp /nologo /I"$I",2
3=5,O,$B\LINK.EXE /SUBSYSTEM:WINDOWS /RELEASE /VERSION:4.0 /LIBPATH:"$L",3,4
4=0,0,,5
5=rsrc.obj,O,$B\CVTRES.EXE,rsrc.res
6=*.obj,O,$B\ML.EXE /c /coff /Cp /nologo /I"$I",*.asm
7=0,0,\OllyDbg\OllyDbg,5
[Group]
Group=Added files,Assembly,Resources,Misc,Modules
1=2
2=2
3=
4=1
5=
[Size]
2=0,0,0,0,3180
1=0,0,1095,875,4
4=0,0,0,0,291
3=
5=
[Resource]
[Colref]
0=00FFFFFF
1=00FFFFFF
2=00FFFFFF
3=00FFFFFF
4=00FFFFFF
5=00FFFFFF
6=00FFFFFF
7=00FFFFFF
[Version-Inc]
Options=52
[BookMark]
0=
1=
2=
3=
4=
5=
6=
7=
8=
9=
[BreakPoint]
0=
[VersionControl]
Settings=1246
Milestones=129
MilestoneEvery=10
MilestoneEveryCurrent=0
MilestoneOnBuild=0.0.0.0
MilestoneOnTime=2
MilestoneOnDate=0
MilestoneOnDateWhen=1
MilestoneOnDateStatus=0
MilestoneOnDateDate=11
MilestoneOnDateTimeYear=2005
MilestoneOnDateTimeMonth=9
MilestoneOnDateTimeDate=10
MilestoneOnDateTimeHour=22
MilestoneOnDateTimeMin=17
MilestoneOnDateTimeSec=43
MilestoneOnDateTimeStatus=0
BackupLocation=C:\masm32\RadASM\Masm\Projects\VCBackups\
CompressionLevel=0
DefaultComment=Project $N, $Z, Backup Created On $D At $T.
ExcludeExt1=\
ExcludeExt2=\
ExcludeExt3=\
ExcludeExt4=\
FileVerLength=4
FileVer2Range=0
FileVer3Range=0
FileVer4Range=0
ProductVerLength=4
ProductVer2Range=0
ProductVer3Range=0
ProductVer4Range=0
[Collapse]
1=
4=
2=
[Find]
1="bExtracted"
2="unpSize"

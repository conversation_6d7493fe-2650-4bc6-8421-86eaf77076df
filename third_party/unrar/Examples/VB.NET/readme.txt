Unpack and open UnRARNET.sln - includes a wrapper for UnRAR.dll and a test program.

Modifued in June 2010 to operate with both 32 and 64-bit versions of UnRAR.

Requires:

- Visual Studio 2008
- .NET 2.0

I have included version of unrar.dll and unrar64.all.  Check the RARLabs website for
a more current version.  They are located in the bin directories for TestRAR and 
unRARNET

If you find any problems, I'd appreciate an email.

<EMAIL>
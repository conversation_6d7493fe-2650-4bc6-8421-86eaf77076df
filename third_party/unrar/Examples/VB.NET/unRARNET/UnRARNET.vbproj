<Project DefaultTargets="Build" ToolsVersion="3.5" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7B86181F-5232-473D-BAFE-477F0DA1DD52}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>UnRARNET</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <AssemblyOriginatorKeyMode>None</AssemblyOriginatorKeyMode>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <OptionCompare>Binary</OptionCompare>
    <OptionExplicit>On</OptionExplicit>
    <OptionStrict>Off</OptionStrict>
    <RootNamespace>RARNET</RootNamespace>
    <StartupObject>RARNET.%28None%29</StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <MyType>Windows</MyType>
    <OldToolsVersion>0.0</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>UnRARNET.xml</DocumentationFile>
    <BaseAddress>285212672</BaseAddress>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>
    </DefineConstants>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>1</WarningLevel>
    <NoWarn>42016,42017,42018,42019,42032</NoWarn>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>UnRARNET.xml</DocumentationFile>
    <BaseAddress>285212672</BaseAddress>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>
    </DefineConstants>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>1</WarningLevel>
    <NoWarn>42016,42017,42018,42019,42032</NoWarn>
    <DebugType>none</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssemblyInfo.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Decompressor.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="pwDlg.vb">
      <SubType>Form</SubType>
    </Compile>
    <EmbeddedResource Include="pwDlg.resx">
      <DependentUpon>pwDlg.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="My Project\" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>
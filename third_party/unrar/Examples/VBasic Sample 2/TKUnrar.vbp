Type=OleDll
Class=CTKUnrar; CTKUnrar.cls
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#D:\WINNT\System32\stdole2.tlb#OLE Automation
Module=MTKUnrar; MTKUnrar.bas
Startup="(None)"
HelpFile=""
ExeName32="TKUnrar.dll"
Path32="..\..\MyProjects\TKUnrar"
Command32=""
Name="TKUnrar"
HelpContextID="0"
CompatibleMode="1"
CompatibleEXE32="..\..\MyProjects\TKUnrar\TKUnrar.dll"
MajorVer=1
MinorVer=0
RevisionVer=0
AutoIncrementVer=0
ServerSupportFiles=0
VersionCompanyName="."
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=0
CodeViewDebugInfo=0
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=1
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1
ThreadingModel=1
DebugStartupOption=0

[MS Transaction Server]
AutoRefresh=1

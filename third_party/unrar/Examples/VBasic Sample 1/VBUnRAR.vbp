Type=Exe
Form=Form1.frm
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#..\..\..\..\SYSTEM\Stdole2.tlb#OLE Automation
IconForm="Form1"
Startup="Form1"
HelpFile=""
Title="UnRAR.dll Visual Basic Example"
ExeName32="VBUnRAR.exe"
Command32="x c:\lamas\ascii.rar 0"
Name="Project1"
HelpContextID="0"
Description="UnRAR.dll Visual Basic Example"
CompatibleMode="0"
MajorVer=1
MinorVer=0
RevisionVer=0
AutoIncrementVer=0
ServerSupportFiles=0
VersionComments="Ported to Visual Basic by <PERSON> Lama<PERSON>  E-mail:  <EMAIL>  "
VersionFileDescription="UnRAR.dll Visual Basic Example"
VersionProductName="UnRAR.dll Visual Basic Example"
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=0
CodeViewDebugInfo=0
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=0
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1

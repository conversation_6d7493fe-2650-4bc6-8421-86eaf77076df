skip_non_tags: true
clone_depth: 1

image:
  - Visual Studio 2015
  - Visual Studio 2017
  - Visual Studio 2019
  - Visual Studio 2022
  - Ubuntu

environment:
  bit7z_version: $(APPVEYOR_REPO_TAG_NAME)
  APPVEYOR_YML_DISABLE_PS_LINUX: true

  matrix:
  - compiler: msvc
    arch: x86
    flags: -DBIT7Z_STATIC_RUNTIME=OFF -DBIT7Z_VS_LIBNAME_OUTDIR_STYLE=ON

  - compiler: msvc
    arch: x64
    flags: -DBIT7Z_STATIC_RUNTIME=OFF -DBIT7Z_VS_LIBNAME_OUTDIR_STYLE=ON

  - compiler: msvc_mt
    arch: x86
    flags: -DBIT7Z_STATIC_RUNTIME=ON -DBIT7Z_VS_LIBNAME_OUTDIR_STYLE=ON

  - compiler: msvc_mt
    arch: x64
    flags: -DBIT7Z_STATIC_RUNTIME=ON -DBIT7Z_VS_LIBNAME_OUTDIR_STYLE=ON

  - compiler: mingw
    arch: x86
    flags:
    PATH: C:\mingw-w64\i686-8.1.0-posix-dwarf-rt_v6-rev0\mingw32\bin;$(PATH)

  - compiler: mingw
    arch: x64
    flags:
    PATH: C:\mingw-w64\x86_64-8.1.0-posix-seh-rt_v6-rev0\mingw64\bin;$(PATH)

  - compiler: gcc
    arch: x86
    flags: -DCMAKE_C_FLAGS=-m32 -DCMAKE_CXX_FLAGS=-m32
    CC: /usr/bin/gcc-9
    CXX: /usr/bin/g++-9

  - compiler: gcc
    arch: x64
    CC: /usr/bin/gcc-9
    CXX: /usr/bin/g++-9

  - compiler: clang
    arch: x86
    flags: -DCMAKE_C_FLAGS=-m32 -DCMAKE_CXX_FLAGS=-m32
    CC: /usr/bin/clang
    CXX: /usr/bin/clang++

  - compiler: clang
    arch: x64
    CC: /usr/bin/clang
    CXX: /usr/bin/clang++

matrix:
  exclude:
  # Excluding GCC and Clang on Windows images, and MSVC on Ubuntu image.
  # Also, we compile MinGW binaries only on Visual Studio 2022 image.
    - image: Visual Studio 2015
      compiler: mingw
    - image: Visual Studio 2015
      compiler: gcc
    - image: Visual Studio 2015
      compiler: clang
    - image: Visual Studio 2017
      compiler: mingw
    - image: Visual Studio 2017
      compiler: gcc
    - image: Visual Studio 2017
      compiler: clang
    - image: Visual Studio 2019
      compiler: mingw
    - image: Visual Studio 2019
      compiler: gcc
    - image: Visual Studio 2019
      compiler: clang
    - image: Visual Studio 2022
      compiler: gcc
    - image: Visual Studio 2022
      compiler: clang
    - image: Ubuntu
      compiler: msvc
    - image: Ubuntu
      compiler: msvc_mt
    - image: Ubuntu
      compiler: mingw

for:
-
  matrix:
    only:
      - image: Ubuntu

  init: |-
    if [ "${compiler}" = "gcc" ];
    then
      compiler_tag=gcc$(${CXX} --version | grep ^g++ | sed 's/^.* //g')_${arch}
    else
      compiler_tag=clang$(${CXX} --version | head -n 1 | sed -e 's/.* version \([0-9.]*\).*/\1/')_${arch}
    fi
    if [ "${arch}" = "x86" ];
    then
      sudo apt-get update;
      sudo apt-get install -y g++-9-multilib;
    fi

  before_build:
    - git submodule update --init --recursive
    - mkdir build
    - cd build

  build_script:
    - cmake .. -G "Ninja" $flags -DCMAKE_BUILD_TYPE=Release
    - cmake --build . -j --config Release
    - cmake .. -G "Ninja" $flags -DCMAKE_BUILD_TYPE=Debug
    - cmake --build . -j --config Debug

  after_build:
    - cd ..
    - mkdir -p pkg/bit7z/
    - mkdir -p pkg/bit7z/include/
    - cp -r lib pkg/bit7z/
    - cp -r include pkg/bit7z/
    - cp README.md pkg/bit7z/
    - cp LICENSE pkg/bit7z/
    - echo $bit7z_version $compiler_tag > "pkg/bit7z/BUILD.txt"
    - cd pkg
    - 7z a -t7z bit7z-$bit7z_version-$compiler_tag.7z *

-
  matrix:
    only:
      - image: Visual Studio 2015
      - image: Visual Studio 2017
      - image: Visual Studio 2019
      - image: Visual Studio 2022

  init:
    - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2022" if NOT "%compiler%"=="mingw" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" %arch:x86=amd64_x86%
      )
    - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2019" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvarsall.bat" %arch:x86=amd64_x86%
      )
    - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2017" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvarsall.bat" %arch:x86=amd64_x86%
      )
    - if "%APPVEYOR_BUILD_WORKER_IMAGE%"=="Visual Studio 2015" if NOT "%compiler%"=="mingw" (
        call "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" %arch:x86=amd64_x86%
      )
    - if "%compiler%"=="mingw" (
        for /f %%i in ('g++ -dumpversion') do set "compiler_tag=mingw%%i_%arch%"
      ) else (
        set "compiler_tag=%APPVEYOR_BUILD_WORKER_IMAGE:Visual Studio =msvc%%compiler:msvc=%_%arch%"
      )

  before_build:
    - git submodule update --init --recursive
    - mkdir build
    - cd build

  build_script:
    - cmake .. -G "Ninja" %flags% -DCMAKE_BUILD_TYPE=Release
    - cmake --build . -j --config Release
    - cmake .. -G "Ninja" %flags% -DCMAKE_BUILD_TYPE=Debug
    - cmake --build . -j --config Debug

  after_build:
    - cd ..
    - mkdir "pkg/bit7z/lib/"
    - mkdir "pkg/bit7z/include/bit7z/"
    - ps: Copy-Item -Path lib\* -Destination pkg\bit7z\lib\ -Recurse
    - copy include\bit7z\bit*.hpp pkg\bit7z\include\bit7z\
    - copy README.md pkg\bit7z\
    - copy LICENSE pkg\bit7z\
    - echo %bit7z_version% %compiler_tag% > "pkg/bit7z/BUILD.txt"
    - cd pkg
    - 7z a "bit7z-%bit7z_version%-%compiler_tag%.7z" *

artifacts:
  - path: pkg\*.7z
    name: binary

test: off

deploy:
  provider: GitHub
  release: Bit7z $(APPVEYOR_REPO_TAG_NAME)
  tag: $(APPVEYOR_REPO_TAG_NAME)
  description: 'Binaries of Bit7z %bit7z_version%'
  auth_token:
    secure: aaQkoEIZv7oaBSZT2f/bST7NfLOdqKXitnhWmUqaW1T493Pr2/atcZWHIlExsLAx
  artifact: /.*\.7z/
  draft: true
  prerelease: false
  on:
    # branch: master
    APPVEYOR_REPO_TAG: true

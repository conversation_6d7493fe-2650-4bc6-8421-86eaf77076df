*.3-pre1
*.TMP
*.a
*.autosave
*.cd
*.code-workspace
*.cppcheck
*.ctu-info
*.db
*.filters
*.ini
*.kdbx
*.lastcodeanalysissucceeded
*.lib
*.opendb
*.opensdf
*.pdb
*.pro.user
*.pro\.user*-pre1
*.regx
*.ruleset
*.sdf
*.suo
*.suppress
*.txt.user
*.vcxproj
*.vcxproj.user

*-cppcheck-*/
.idea/
.vs/
.vscode/
bin/
build/
cmake-*/
docker/
docs/html/
docs/md/
docs/rst/
docs/wiki/
docs/xml/
gh-pages/
lib/
out/
pack/
third_party/
x64/

.mailmap
.qmake.stash
CMakeSettings.json
bit7z.sln
roadmap.txt

# PVS-Studio files and directories
*.PVS-Studio.i
*.cfg
*.dfo
.PVS-Studio/
pvs-studio-analyzer.stacktrace.txt

# Test data
tests/data/

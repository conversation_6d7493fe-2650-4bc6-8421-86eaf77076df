; 7z supporting for MutiArc in Far 
; Append the following strings to file 
; ..\Program Files\Far\Plugins\MultiArc\Formats\Custom.ini
 
[7z]
TypeName=7z
ID=37 7A BC AF 27 1C
IDPos=
IDOnly=1
Extension=7z
List=7z l -- %%AQ
Start="^-----"
End="^-----"
Format0="yyyy tt dd hh mm ss aaaaa zzzzzzzzzzzz pppppppppppp  nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn"
Extract=7z x {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
ExtractWithoutPath=7z e {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
Test=7z t {-p%%P} -r0 -scsDOS -i@%%LQMN -- %%A
Delete=7z d {-p%%P} -r0 -ms=off -scsDOS -i@%%LQMN -- %%A
Add=7z a {-p%%P} -r0 -t7z {%%S} -scsDOS -i@%%LQMN -- %%A
AddRecurse=7z a {-p%%P} -r0 -t7z {%%S} -scsDOS -i@%%LQMN -- %%A
AllFilesMask="*"

[rpm]
TypeName=rpm
ID=ED AB EE DB
IDPos=
IDOnly=1
Extension=rpm
List=7z l -- %%AQ
Start="^-----"
End="^-----"
Format0="yyyy tt dd hh mm ss aaaaa zzzzzzzzzzzz pppppppppppp  nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn"
Extract=7z x {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
ExtractWithoutPath=7z e {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
Test=7z t {-p%%P} -r0 -scsDOS -i@%%LQMN -- %%A
AllFilesMask="*"

[cpio]
TypeName=cpio
ID=
IDPos=
IDOnly=0
Extension=cpio
List=7z l -- %%AQ
Start="^-----"
End="^-----"
Format0="yyyy tt dd hh mm ss aaaaa zzzzzzzzzzzz pppppppppppp  nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn"
Extract=7z x {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
ExtractWithoutPath=7z e {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
Test=7z t {-p%%P} -r0 -scsDOS -i@%%LQMN -- %%A
AllFilesMask="*"

[deb]
TypeName=deb
ID=
IDPos=
IDOnly=0
Extension=deb
List=7z l -- %%AQ
Start="^-----"
End="^-----"
Format0="yyyy tt dd hh mm ss aaaaa zzzzzzzzzzzz pppppppppppp  nnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn"
Extract=7z x {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
ExtractWithoutPath=7z e {-p%%P} -r0 -y -scsDOS -i@%%LQMN -- %%A
Test=7z t {-p%%P} -r0 -scsDOS -i@%%LQMN -- %%A
AllFilesMask="*"


#define IDD_PROGRESS       97
#define IDD_PROGRESS_2  10097

#define IDS_CLOSE                  408
#define IDS_CONTINUE               411

#define IDB_PROGRESS_BACKGROUND    444
#define IDS_PROGRESS_FOREGROUND    445
#define IDB_PAUSE                  446
#define IDS_PROGRESS_PAUSED        447
#define IDS_PROGRESS_ASK_CANCEL    448

#define IDT_PROGRESS_PACKED       1008
#define IDT_PROGRESS_FILES        1032

#define IDT_PROGRESS_ELAPSED      3900
#define IDT_PROGRESS_REMAINING    3901
#define IDT_PROGRESS_TOTAL        3902
#define IDT_PROGRESS_SPEED        3903
#define IDT_PROGRESS_PROCESSED    3904
#define IDT_PROGRESS_RATIO        3905
#define IDT_PROGRESS_ERRORS       3906

#define IDC_PROGRESS1              100
#define IDL_PROGRESS_MESSAGES      101
#define IDT_PROGRESS_FILE_NAME     102
#define IDT_PROGRESS_STATUS        103

#define IDT_PROGRESS_PACKED_VAL    110
#define IDT_PROGRESS_FILES_VAL     111

#define IDT_PROGRESS_ELAPSED_VAL   120
#define IDT_PROGRESS_REMAINING_VAL 121
#define IDT_PROGRESS_TOTAL_VAL     122
#define IDT_PROGRESS_SPEED_VAL     123
#define IDT_PROGRESS_PROCESSED_VAL 124
#define IDT_PROGRESS_RATIO_VAL     125
#define IDT_PROGRESS_ERRORS_VAL    126


#ifdef UNDER_CE
#define MY_PROGRESS_VAL_UNITS  44
#else
#define MY_PROGRESS_VAL_UNITS  76
#endif
#define MY_PROGRESS_LABEL_UNITS_MIN   60
#define MY_PROGRESS_LABEL_UNITS_START 90
#define MY_PROGRESS_PAD_UNITS  4

#include "ProgressDialog2Res.h"
#include "../../GuiCommon.rc"

#undef DIALOG_ID
#define DIALOG_ID  IDD_PROGRESS
#define xc 360
#define k 11
#define z1s 16

#include "ProgressDialog2a.rc"

#ifdef UNDER_CE

#include "../../GuiCommon.rc"


#undef DIALOG_ID
#undef m
#undef k
#undef z1s

#define DIALOG_ID  IDD_PROGRESS_2
#define m 4
#define k 8
#define z1s 12

#define xc 280

#include "ProgressDialog2a.rc"

#endif

STRINGTABLE DISCARDABLE
{
  IDS_PROGRESS_PAUSED     "Paused"
  IDS_PROGRESS_FOREGROUND "&Foreground"
  IDS_CONTINUE            "&Continue"
  IDS_PROGRESS_ASK_CANCEL "Are you sure you want to cancel?"
  IDS_CLOSE "&Close"
}

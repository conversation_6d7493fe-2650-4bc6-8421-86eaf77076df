PROG = 7zcl.exe
MY_CONSOLE = 1

CURRENT_OBJS = \
  $O\Client7z.obj \

COMMON_OBJS = \
  $O\IntToString.obj \
  $O\NewHandler.obj \
  $O\MyString.obj \
  $O\StringConvert.obj \
  $O\StringToInt.obj \
  $O\MyVector.obj \
  $O\Wildcard.obj \

WIN_OBJS = \
  $O\DLL.obj \
  $O\FileDir.obj \
  $O\FileFind.obj \
  $O\FileIO.obj \
  $O\FileName.obj \
  $O\PropVariant.obj \
  $O\PropVariantConv.obj \

7ZIP_COMMON_OBJS = \
  $O\FileStreams.obj \

!include "../../7zip.mak"

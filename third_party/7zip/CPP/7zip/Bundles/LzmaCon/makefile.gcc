PROG = lzma
CXX = g++ -O2
# -Wall -Werror -Wno-delete-non-virtual-dtor
CXX_C = gcc -O2 -Wall -Werror

ifdef SystemDrive
IS_MINGW = 1
endif

ifdef IS_MINGW

RM = del
CFLAGS = -c
LIB2 = -loleaut32 -luuid
LDFLAGS = -s

FILE_IO =FileIO
FILE_IO_2 =Windows/$(FILE_IO)

MT_FILES = \
  LzFindMt.o \
  Threads.o \

else

RM = rm -f
CFLAGS = -c -D_7ZIP_ST

FILE_IO =C_FileIO
FILE_IO_2 =Common/$(FILE_IO)


endif


OBJS = \
  $(MT_FILES) \
  $(FILE_IO).o \
  LzmaAlone.o \
  Bench.o \
  BenchCon.o \
  ConsoleClose.o \
  LzmaDecoder.o \
  LzmaEncoder.o \
  LzmaRegister.o \
  CreateCoder.o \
  CWrappers.o \
  FileStreams.o \
  FilterCoder.o \
  MethodProps.o \
  StreamUtils.o \
  CommandLineParser.o \
  CRC.o \
  CrcReg.o \
  IntToString.o \
  MyString.o \
  MyVector.o \
  MyWindows.o \
  StringConvert.o \
  StringToInt.o \
  PropVariant.o \
  System.o \
  7zCrc.o \
  7zCrcOpt.o \
  Alloc.o \
  Bra86.o \
  CpuArch.o \
  LzFind.o \
  LzmaDec.o \
  LzmaEnc.o \
  Lzma86Dec.o \
  Lzma86Enc.o \


all: $(PROG)

$(PROG): $(OBJS)
	$(CXX) -o $(PROG) $(LDFLAGS) $(OBJS) $(LIB2)

LzmaAlone.o: LzmaAlone.cpp
	$(CXX) $(CFLAGS) LzmaAlone.cpp

Bench.o: ../../UI/Common/Bench.cpp
	$(CXX) $(CFLAGS) ../../UI/Common/Bench.cpp

BenchCon.o: ../../UI/Console/BenchCon.cpp
	$(CXX) $(CFLAGS) ../../UI/Console/BenchCon.cpp

ConsoleClose.o: ../../UI/Console/ConsoleClose.cpp
	$(CXX) $(CFLAGS) ../../UI/Console/ConsoleClose.cpp

LzmaDecoder.o: ../../Compress/LzmaDecoder.cpp
	$(CXX) $(CFLAGS) ../../Compress/LzmaDecoder.cpp

LzmaEncoder.o: ../../Compress/LzmaEncoder.cpp
	$(CXX) $(CFLAGS) ../../Compress/LzmaEncoder.cpp

LzmaRegister.o: ../../Compress/LzmaRegister.cpp
	$(CXX) $(CFLAGS) ../../Compress/LzmaRegister.cpp

CreateCoder.o: ../../Common/CreateCoder.cpp
	$(CXX) $(CFLAGS) ../../Common/CreateCoder.cpp

CWrappers.o: ../../Common/CWrappers.cpp
	$(CXX) $(CFLAGS) ../../Common/CWrappers.cpp

FileStreams.o: ../../Common/FileStreams.cpp
	$(CXX) $(CFLAGS) ../../Common/FileStreams.cpp

FilterCoder.o: ../../Common/FilterCoder.cpp
	$(CXX) $(CFLAGS) ../../Common/FilterCoder.cpp

MethodProps.o: ../../Common/MethodProps.cpp
	$(CXX) $(CFLAGS) ../../Common/MethodProps.cpp

StreamUtils.o: ../../Common/StreamUtils.cpp
	$(CXX) $(CFLAGS) ../../Common/StreamUtils.cpp

$(FILE_IO).o: ../../../$(FILE_IO_2).cpp
	$(CXX) $(CFLAGS) ../../../$(FILE_IO_2).cpp


CommandLineParser.o: ../../../Common/CommandLineParser.cpp
	$(CXX) $(CFLAGS) ../../../Common/CommandLineParser.cpp

CRC.o: ../../../Common/CRC.cpp
	$(CXX) $(CFLAGS) ../../../Common/CRC.cpp

CrcReg.o: ../../../Common/CrcReg.cpp
	$(CXX) $(CFLAGS) ../../../Common/CrcReg.cpp

IntToString.o: ../../../Common/IntToString.cpp
	$(CXX) $(CFLAGS) ../../../Common/IntToString.cpp

MyString.o: ../../../Common/MyString.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyString.cpp

MyVector.o: ../../../Common/MyVector.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyVector.cpp

MyWindows.o: ../../../Common/MyWindows.cpp
	$(CXX) $(CFLAGS) ../../../Common/MyWindows.cpp

StringConvert.o: ../../../Common/StringConvert.cpp
	$(CXX) $(CFLAGS) ../../../Common/StringConvert.cpp

StringToInt.o: ../../../Common/StringToInt.cpp
	$(CXX) $(CFLAGS) ../../../Common/StringToInt.cpp

PropVariant.o: ../../../Windows/PropVariant.cpp
	$(CXX) $(CFLAGS) ../../../Windows/PropVariant.cpp

System.o: ../../../Windows/System.cpp
	$(CXX) $(CFLAGS) ../../../Windows/System.cpp

7zCrc.o: ../../../../C/7zCrc.c
	$(CXX_C) $(CFLAGS) ../../../../C/7zCrc.c

7zCrcOpt.o: ../../../../C/7zCrcOpt.c
	$(CXX_C) $(CFLAGS) ../../../../C/7zCrcOpt.c

Alloc.o: ../../../../C/Alloc.c
	$(CXX_C) $(CFLAGS) ../../../../C/Alloc.c

Bra86.o: ../../../../C/Bra86.c
	$(CXX_C) $(CFLAGS) ../../../../C/Bra86.c

CpuArch.o: ../../../../C/CpuArch.c
	$(CXX_C) $(CFLAGS) ../../../../C/CpuArch.c

LzFind.o: ../../../../C/LzFind.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzFind.c

ifdef MT_FILES
LzFindMt.o: ../../../../C/LzFindMt.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzFindMt.c

Threads.o: ../../../../C/Threads.c
	$(CXX_C) $(CFLAGS) ../../../../C/Threads.c
endif

LzmaDec.o: ../../../../C/LzmaDec.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaDec.c

LzmaEnc.o: ../../../../C/LzmaEnc.c
	$(CXX_C) $(CFLAGS) ../../../../C/LzmaEnc.c

Lzma86Dec.o: ../../../../C/Lzma86Dec.c
	$(CXX_C) $(CFLAGS) ../../../../C/Lzma86Dec.c

Lzma86Enc.o: ../../../../C/Lzma86Enc.c
	$(CXX_C) $(CFLAGS) ../../../../C/Lzma86Enc.c

clean:
	-$(RM) $(PROG) $(OBJS)

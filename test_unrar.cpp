// 简单的UnRAR.dll测试程序
#include <windows.h>
#include <iostream>
#include <string>

int main() {
    std::cout << "Testing UnRAR.dll loading..." << std::endl;
    
    // 尝试加载UnRAR.dll
    std::string dllPath = "third_party/unrar/UnRAR.dll";
    HMODULE hUnRAR = LoadLibraryA(dllPath.c_str());
    
    if (!hUnRAR) {
        std::cout << "Failed to load UnRAR.dll from: " << dllPath << std::endl;
        std::cout << "Error code: " << GetLastError() << std::endl;
        return 1;
    }
    
    std::cout << "Successfully loaded UnRAR.dll!" << std::endl;
    
    // 尝试获取API函数
    FARPROC openFunc = GetProcAddress(hUnRAR, "RAROpenArchiveDataEx");
    FARPROC closeFunc = GetProcAddress(hUnRAR, "RARCloseArchive");
    FARPROC readFunc = GetProcAddress(hUnRAR, "RARReadHeaderEx");
    FARPROC processFunc = GetProcAddress(hUnRAR, "RARProcessFile");
    
    std::cout << "API Functions:" << std::endl;
    std::cout << "RAROpenArchiveDataEx: " << (openFunc ? "Found" : "Not found") << std::endl;
    std::cout << "RARCloseArchive: " << (closeFunc ? "Found" : "Not found") << std::endl;
    std::cout << "RARReadHeaderEx: " << (readFunc ? "Found" : "Not found") << std::endl;
    std::cout << "RARProcessFile: " << (processFunc ? "Found" : "Not found") << std::endl;
    
    FreeLibrary(hUnRAR);
    
    if (openFunc && closeFunc && readFunc && processFunc) {
        std::cout << "All required API functions found! UnRAR.dll is compatible." << std::endl;
        return 0;
    } else {
        std::cout << "Some API functions missing. UnRAR.dll may not be compatible." << std::endl;
        return 1;
    }
}

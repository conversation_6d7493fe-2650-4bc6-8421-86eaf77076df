cmake_minimum_required(VERSION 3.16)
project(Bit7zDebug VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)

# Find Qt6 components
find_package(Qt6 COMPONENTS
        Core
        REQUIRED)

# Set third-party library paths
set(THIRD_PARTY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third_party")
set(BIT7Z_DIR "${THIRD_PARTY_DIR}/bit7z")
set(SEVENZIP_DIR "${THIRD_PARTY_DIR}/7zip")

# Check for project-local bit7z source code
set(BIT7Z_FOUND FALSE)
if(EXISTS "${BIT7Z_DIR}/include/bit7z/bit7zlibrary.hpp" AND EXISTS "${BIT7Z_DIR}/src")
    set(BIT7Z_FOUND TRUE)
    set(BIT7Z_INCLUDE_DIRS "${BIT7Z_DIR}/include")
    
    # Collect bit7z source files
    file(GLOB BIT7Z_SOURCES "${BIT7Z_DIR}/src/*.cpp")
    file(GLOB BIT7Z_INTERNAL_SOURCES "${BIT7Z_DIR}/src/internal/*.cpp")
    list(APPEND BIT7Z_SOURCES ${BIT7Z_INTERNAL_SOURCES})
    
    list(LENGTH BIT7Z_SOURCES BIT7Z_SOURCE_COUNT)
    message(STATUS "Found project-local bit7z source code with ${BIT7Z_SOURCE_COUNT} files")
endif()

# Add source files
set(SOURCES
    debug_bit7z.cpp
)

# Add bit7z source files if available
if(BIT7Z_FOUND AND BIT7Z_SOURCES)
    list(APPEND SOURCES ${BIT7Z_SOURCES})
    message(STATUS "Added ${BIT7Z_SOURCE_COUNT} bit7z source files to debug build")
endif()

add_executable(Bit7zDebug ${SOURCES})

# Configure bit7z
if(BIT7Z_FOUND)
    target_include_directories(Bit7zDebug PRIVATE 
        ${BIT7Z_INCLUDE_DIRS}
        ${BIT7Z_DIR}/include/bit7z
        ${BIT7Z_DIR}/src
        ${SEVENZIP_DIR}/CPP
    )
    target_link_libraries(Bit7zDebug
            Qt6::Core
    )
    target_compile_definitions(Bit7zDebug PRIVATE 
        HAVE_BIT7Z
        SEVENZIP_2301
    )
    message(STATUS "bit7z debug build configured")
else()
    target_link_libraries(Bit7zDebug
            Qt6::Core
    )
    message(WARNING "bit7z not found for debug build")
endif()

# Copy 7z.dll to output directory
if(EXISTS "${SEVENZIP_DIR}/7z.dll")
    add_custom_command(TARGET Bit7zDebug POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy
            "${SEVENZIP_DIR}/7z.dll"
            "$<TARGET_FILE_DIR:Bit7zDebug>")
    message(STATUS "Will copy 7z.dll to debug output directory")
endif()

# Set output directory
set_target_properties(Bit7zDebug PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/debug"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/debug"
)

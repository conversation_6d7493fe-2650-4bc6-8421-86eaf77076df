{"version": 3, "configurePresets": [{"name": "Qt-Debug", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_CXX_FLAGS": "-DQT_QML_DEBUG"}, "environment": {"QML_DEBUG_ARGS": "-qmljsdebugger=file:{3ac98940-07da-4c08-b641-ec376a4ecb12},block"}}, {"name": "Qt-Release", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/release", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"hidden": true, "name": "Qt-<PERSON><PERSON><PERSON>", "inherits": "11", "vendor": {"qt-project.org/Default": {"checksum": "2q+kLYeG62pDs62/yfl655sJJ2U="}}}, {"hidden": true, "name": "11", "inherits": "Qt", "environment": {"QTDIR": "D:/Qt/6.9.1/msvc2022_64"}, "architecture": {"strategy": "external", "value": "x64"}, "generator": "Ninja", "vendor": {"qt-project.org/Version": {"checksum": "/uC+pb9WcfJro9qG7e7vK3TcMdQ="}}}], "vendor": {"qt-project.org/Presets": {"checksum": "i0OG3aelt4vjKdHOwS52dSeo+T8="}}}
@echo off
echo.
echo ========================================
echo     Download bit7z for BandiZip
echo ========================================
echo.

echo This script will help you download bit7z library.
echo.
echo Option 1: Manual download (Recommended)
echo   1. Visit: https://github.com/rikyoz/bit7z/releases
echo   2. Download the latest "Source code (zip)"
echo   3. Extract and copy include/bit7z/* to third_party/bit7z/include/bit7z/
echo.
echo Option 2: Use vcpkg (if you have it)
echo   vcpkg install bit7z --triplet x64-windows
echo.
echo Option 3: Continue without bit7z
echo   Project will work fine with 7z.dll fallback mode
echo.

set /p choice="Choose option (1/2/3): "

if "%choice%"=="1" goto :manual
if "%choice%"=="2" goto :vcpkg
if "%choice%"=="3" goto :fallback

:manual
echo.
echo [MANUAL] Opening GitHub releases page...
start https://github.com/rikyoz/bit7z/releases
echo.
echo After downloading:
echo 1. Extract the zip file
echo 2. Copy include/bit7z/* to third_party/bit7z/include/bit7z/
echo 3. Run this script again to verify
echo.
pause
goto :verify

:vcpkg
echo.
echo [VCPKG] Checking for vcpkg...
where vcpkg >nul 2>&1
if %errorlevel% neq 0 (
    echo vcpkg not found. Please install vcpkg first.
    goto :end
)

echo Installing bit7z...
vcpkg install bit7z --triplet x64-windows
if %errorlevel% neq 0 (
    echo Installation failed.
    goto :end
)

echo Copying files...
if exist "C:\vcpkg\installed\x64-windows\include\bit7z" (
    xcopy "C:\vcpkg\installed\x64-windows\include\bit7z" "third_party\bit7z\include\bit7z\" /E /I /Y
    echo Headers copied successfully.
)

goto :verify

:fallback
echo.
echo [FALLBACK] Continuing without bit7z
echo.
echo Your project will use 7z.dll fallback mode:
echo - Fully functional compression/decompression
echo - Compatible with all formats
echo - Good performance
echo.
echo You can add bit7z later for even better performance.
goto :end

:verify
echo.
echo [VERIFY] Checking bit7z integration...
echo.

if exist "third_party\bit7z\include\bit7z\bit7z.hpp" (
    echo [OK] bit7z.hpp found
    set BIT7Z_OK=1
) else (
    echo [MISSING] bit7z.hpp not found
    set BIT7Z_OK=0
)

if exist "third_party\7zip\7z.dll" (
    echo [OK] 7z.dll found
    set DLL_OK=1
) else (
    echo [MISSING] 7z.dll not found
    set DLL_OK=0
)

echo.
if %BIT7Z_OK%==1 if %DLL_OK%==1 (
    echo [SUCCESS] bit7z integration complete!
    echo.
    echo Your project now has:
    echo - High-performance bit7z C++ interface
    echo - 7z.dll compression engine
    echo - Real-time progress callbacks
    echo - Modern exception handling
    echo.
    echo Next steps:
    echo 1. Build project: .\build_qt6.bat
    echo 2. Test compression features
) else (
    echo [INCOMPLETE] Integration not complete
    echo.
    if %BIT7Z_OK%==0 echo - Download bit7z headers from GitHub
    if %DLL_OK%==0 echo - Ensure 7z.dll is in third_party\7zip\
)

:end
echo.
pause

/*
 * bit7z调试测试程序
 * 用于验证bit7z库的初始化和基本功能
 */

#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <iostream>

#ifdef HAVE_BIT7Z
#include <bit7z/bit7zlibrary.hpp>
#include <bit7z/bitfileextractor.hpp>
#include <bit7z/bitfilecompressor.hpp>
#include <bit7z/bitformat.hpp>
#include <bit7z/bitexception.hpp>
using namespace bit7z;
#endif

void testBit7zInitialization()
{
    std::cout << "\n========================================" << std::endl;
    std::cout << "     bit7z Initialization Test" << std::endl;
    std::cout << "========================================\n" << std::endl;

#ifdef HAVE_BIT7Z
    std::cout << "✅ HAVE_BIT7Z is defined" << std::endl;
    
    // 测试路径
    QStringList dllPaths = {
        QApplication::applicationDirPath() + "/7z.dll",
        QApplication::applicationDirPath() + "/../third_party/7zip/7z.dll",
        QDir::currentPath() + "/third_party/7zip/7z.dll",
        "third_party/7zip/7z.dll",
        "C:/Program Files/7-Zip/7z.dll",
        "C:/Program Files (x86)/7-Zip/7z.dll"
    };

    std::cout << "Application directory: " << QApplication::applicationDirPath().toStdString() << std::endl;
    std::cout << "Current directory: " << QDir::currentPath().toStdString() << std::endl;
    std::cout << "\nSearching for 7z.dll..." << std::endl;

    for (const QString &path : dllPaths) {
        QFileInfo fileInfo(path);
        std::cout << "  " << path.toStdString() << " -> " 
                  << (fileInfo.exists() ? "EXISTS" : "NOT FOUND") << std::endl;
        
        if (fileInfo.exists()) {
            std::cout << "    Size: " << fileInfo.size() << " bytes" << std::endl;
            std::cout << "    Absolute path: " << fileInfo.absoluteFilePath().toStdString() << std::endl;
            
            try {
                std::cout << "    Attempting bit7z initialization..." << std::endl;
                Bit7zLibrary lib(fileInfo.absoluteFilePath().toStdString());
                std::cout << "    ✅ bit7z initialization SUCCESS!" << std::endl;
                
                // 测试基本功能
                try {
                    std::cout << "    Testing BitFileExtractor creation..." << std::endl;
                    BitFileExtractor extractor(lib);
                    std::cout << "    ✅ BitFileExtractor creation SUCCESS!" << std::endl;
                } catch (const BitException& e) {
                    std::cout << "    ❌ BitFileExtractor creation failed: " << e.what() << std::endl;
                } catch (const std::exception& e) {
                    std::cout << "    ❌ BitFileExtractor creation failed: " << e.what() << std::endl;
                }
                
                try {
                    std::cout << "    Testing BitFileCompressor creation..." << std::endl;
                    BitFileCompressor compressor(lib, BitFormat::Zip);
                    std::cout << "    ✅ BitFileCompressor creation SUCCESS!" << std::endl;
                } catch (const BitException& e) {
                    std::cout << "    ❌ BitFileCompressor creation failed: " << e.what() << std::endl;
                } catch (const std::exception& e) {
                    std::cout << "    ❌ BitFileCompressor creation failed: " << e.what() << std::endl;
                }
                
                std::cout << "    🎉 bit7z is fully functional with this 7z.dll!" << std::endl;
                return;
                
            } catch (const BitException& e) {
                std::cout << "    ❌ bit7z initialization failed: " << e.what() << std::endl;
            } catch (const std::exception& e) {
                std::cout << "    ❌ bit7z initialization failed: " << e.what() << std::endl;
            }
        }
    }
    
    std::cout << "\n❌ No working 7z.dll found for bit7z" << std::endl;
#else
    std::cout << "❌ HAVE_BIT7Z is NOT defined - bit7z not compiled in" << std::endl;
#endif
}

void testFileCreation()
{
    std::cout << "\n========================================" << std::endl;
    std::cout << "     File Creation Test" << std::endl;
    std::cout << "========================================\n" << std::endl;
    
    // 创建测试文件
    QString testFile = "test_file.txt";
    QFile file(testFile);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << "This is a test file for bit7z compression/extraction testing.";
        file.close();
        std::cout << "✅ Test file created: " << testFile.toStdString() << std::endl;
    } else {
        std::cout << "❌ Failed to create test file" << std::endl;
    }
}

void testExtraction()
{
    std::cout << "\n========================================" << std::endl;
    std::cout << "     Extraction Test" << std::endl;
    std::cout << "========================================\n" << std::endl;

#ifdef HAVE_BIT7Z
    // 查找可用的7z.dll
    QStringList dllPaths = {
        QApplication::applicationDirPath() + "/7z.dll",
        QApplication::applicationDirPath() + "/../third_party/7zip/7z.dll",
        QDir::currentPath() + "/third_party/7zip/7z.dll",
        "third_party/7zip/7z.dll"
    };

    for (const QString &path : dllPaths) {
        QFileInfo fileInfo(path);
        if (fileInfo.exists()) {
            try {
                std::cout << "Using 7z.dll: " << path.toStdString() << std::endl;
                Bit7zLibrary lib(fileInfo.absoluteFilePath().toStdString());
                
                // 这里可以添加实际的解压测试
                // 需要一个测试用的压缩包文件
                
                std::cout << "✅ bit7z library ready for extraction" << std::endl;
                return;
                
            } catch (const std::exception& e) {
                std::cout << "❌ Failed with " << path.toStdString() << ": " << e.what() << std::endl;
            }
        }
    }
    
    std::cout << "❌ No working bit7z library found" << std::endl;
#else
    std::cout << "❌ bit7z not available" << std::endl;
#endif
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    std::cout << "bit7z Debug Test Program" << std::endl;
    std::cout << "========================" << std::endl;
    
    testBit7zInitialization();
    testFileCreation();
    testExtraction();
    
    std::cout << "\n========================================" << std::endl;
    std::cout << "     Debug Test Complete" << std::endl;
    std::cout << "========================================" << std::endl;
    
    return 0;
}

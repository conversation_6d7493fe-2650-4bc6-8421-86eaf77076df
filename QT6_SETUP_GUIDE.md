# 🚀 Qt6版本安装和启动指南

## 为什么选择Qt6？

✅ **最新技术** - 支持最新的C++特性和现代化API  
✅ **更好性能** - 优化的渲染引擎和内存管理  
✅ **长期支持** - Qt6.5+ 是LTS版本，长期维护  
✅ **现代化UI** - 更好的高DPI支持和现代化控件  

## 📥 Qt6快速安装方案

### 方案1：Qt官方在线安装器（推荐）

1. **下载Qt6安装器**
   ```
   访问：https://www.qt.io/download-qt-installer
   下载：qt-unified-windows-x64-online.exe (约2MB)
   ```

2. **安装Qt6.5.3 LTS组件**
   - 创建Qt账户（免费）
   - 选择开源版本
   - 在组件选择中勾选：
     - ✅ Qt 6.5.3 (LTS)
     - ✅ MinGW 11.2.0 64-bit 编译器
     - ✅ Qt Creator 11.x IDE
     - ✅ CMake 工具
     - ✅ Ninja 构建工具

3. **验证安装**
   ```cmd
   # 打开新的命令提示符
   qmake --version
   # 应该显示：Qt version 6.5.3
   
   gcc --version
   # 应该显示MinGW版本信息
   ```

### 方案2：使用vcpkg包管理器

1. **安装vcpkg**
   ```cmd
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   ```

2. **安装Qt6**
   ```cmd
   .\vcpkg install qt6[core,widgets] --triplet x64-windows
   ```

3. **设置环境变量**
   ```cmd
   set CMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake
   ```

### 方案3：使用Chocolatey（Windows包管理器）

1. **安装Chocolatey**
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force
   iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
   ```

2. **安装Qt6**
   ```cmd
   choco install qt6
   ```

## 🛠️ 环境配置

### 设置环境变量
```cmd
# 添加到系统PATH（根据实际安装路径调整）
C:\Qt\6.5.3\mingw_64\bin
C:\Qt\Tools\mingw1120_64\bin
C:\Qt\Tools\CMake_64\bin

# 设置CMAKE_PREFIX_PATH
set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64
```

### 验证环境
```cmd
qmake --version     # Qt版本信息
cmake --version     # CMake版本信息
gcc --version       # 编译器版本信息
```

## 🏃‍♂️ 启动项目

### 使用Qt Creator（最简单）
1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择项目根目录的 `CMakeLists.txt`
4. 选择Qt6.5.3 MinGW套件
5. 点击绿色运行按钮

### 使用命令行构建
```cmd
# 设置Qt6路径
set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64

# 生成构建文件
cmake -B build -S . -G "MinGW Makefiles"

# 编译项目
cmake --build build --config Release

# 运行程序
.\build\BandiZip.exe
```

### 使用Visual Studio
```cmd
# 生成VS项目文件
cmake -B build -S . -G "Visual Studio 17 2022"

# 打开解决方案
start build\BandiZip.sln
```

## 🎯 预期效果

程序启动后您将看到现代化的界面：

```
┌─────────────────────────────────────────────────────────────────┐
│ 📦 好压万能压缩                    立即登录  立即开通  ─  □  ✕ │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│    │     📂      │  │     📁      │  │     🎬      │           │
│    │             │  │             │  │             │           │
│    │    解压     │  │    压缩     │  │  视频压缩   │           │
│    │点击/拖入压缩│  │点击/拖入文件│  │支持MP4、WMV │           │
│    │包，一键快速 │  │，支持快速压│  │、AVI等多种  │           │
│    │解压         │  │缩为Zip、7z  │  │格式         │           │
│    └─────────────┘  └─────────────┘  └─────────────┘           │
│                                                                 │
│    ┌─────────────┐  ┌─────────────┐                            │
│    │     🖼️      │  │     📄      │                            │
│    │             │  │             │                            │
│    │  图片压缩   │  │  PDF压缩    │                            │
│    │支持JPG、PNG │  │高效压缩，保 │                            │
│    │、GIF、BMP等 │  │持文档质量   │                            │
│    │多种格式     │  │             │                            │
│    └─────────────┘  └─────────────┘                            │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 Qt6版本特性

### 技术优势
- ✅ **现代C++支持** - 完整的C++17/20特性支持
- ✅ **更好的性能** - 优化的渲染管线和内存管理
- ✅ **高DPI支持** - 完美的4K/8K显示器支持
- ✅ **现代化API** - 更简洁、更安全的API设计

### 界面优势
- ✅ **流畅动画** - 硬件加速的动画效果
- ✅ **现代化控件** - 更美观的默认样式
- ✅ **响应式布局** - 更好的窗口缩放支持
- ✅ **主题支持** - 支持深色/浅色主题切换

### 功能完整性
- ✅ 所有核心功能完全支持
- ✅ 现代化卡片式UI设计
- ✅ 完整的拖拽支持
- ✅ 多线程压缩处理
- ✅ 实时进度显示

## 🆘 常见问题解决

### Q: 提示"找不到Qt6"
**A:** 设置正确的CMAKE_PREFIX_PATH
```cmd
set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64
```

### Q: 编译错误"cannot find -lQt6Core"
**A:** 检查Qt6路径和编译器匹配
```cmd
# 确保使用Qt6自带的MinGW
where gcc
# 应该指向 C:\Qt\Tools\mingw1120_64\bin\gcc.exe
```

### Q: 程序启动后DLL错误
**A:** 确保Qt6 DLL文件可访问
```cmd
# 方法1：添加到PATH
set PATH=C:\Qt\6.5.3\mingw_64\bin;%PATH%

# 方法2：使用windeployqt工具
windeployqt.exe .\build\BandiZip.exe
```

### Q: CMake版本太低
**A:** 升级CMake到3.16+
```cmd
# 下载最新CMake
# https://cmake.org/download/
```

### Q: 7-Zip功能不工作
**A:** 安装7-Zip到默认路径
```
下载：https://www.7-zip.org/download.html
安装到：C:\Program Files\7-Zip\
```

## 📊 推荐配置

### 最小配置
- Windows 10 1809+
- 4GB RAM
- 2GB 可用磁盘空间
- Qt 6.5.3 + MinGW 11.2.0

### 推荐配置
- Windows 11
- 8GB+ RAM
- 5GB+ 可用磁盘空间
- Qt 6.5.3 + Visual Studio 2022
- SSD硬盘

## 🎉 成功标志

如果看到以下内容，说明Qt6项目启动成功：
- ✅ 现代化的卡片式界面
- ✅ 流畅的悬停动画效果
- ✅ 高清晰度的图标显示
- ✅ 完整的拖拽功能
- ✅ 响应式窗口布局

## 📞 技术支持

如果遇到问题：
1. 确保Qt6版本为6.5.3或更高
2. 检查MinGW编译器正确安装
3. 验证CMAKE_PREFIX_PATH设置
4. 使用Qt Creator可以避免大部分配置问题
5. 参考官方文档：https://doc.qt.io/qt-6/

---

**Qt6版本 - 现代化、高性能、面向未来！** 🚀

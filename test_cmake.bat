@echo off
echo Testing CMake configuration...
echo.

REM Clean any existing build files
if exist build rmdir /s /q build 2>nul
if exist CMakeCache.txt del CMakeCache.txt 2>nul
if exist CMakeFiles rmdir /s /q CMakeFiles 2>nul

echo [TEST] CMakeLists.txt configuration
echo.

REM Check if CMakeLists.txt exists and has correct content
if not exist CMakeLists.txt (
    echo [ERROR] CMakeLists.txt not found
    goto :end
)

echo [OK] CMakeLists.txt found
echo.

REM Check for Qt6 configuration
findstr /C:"find_package(Qt6" CMakeLists.txt >nul
if %errorlevel% equ 0 (
    echo [OK] Qt6 configuration found in CMakeLists.txt
) else (
    echo [ERROR] Qt6 configuration not found in CMakeLists.txt
    goto :end
)

REM Check for bit7z integration
findstr /C:"bit7z" CMakeLists.txt >nul
if %errorlevel% equ 0 (
    echo [OK] bit7z integration found in CMakeLists.txt
) else (
    echo [WARNING] bit7z integration not found in CMakeLists.txt
)

REM Check for source files
findstr /C:"src/MainWindow.cpp" CMakeLists.txt >nul
if %errorlevel% equ 0 (
    echo [OK] Source files configured in CMakeLists.txt
) else (
    echo [ERROR] Source files not configured in CMakeLists.txt
    goto :end
)

REM Check if source files exist
if not exist src\MainWindow.cpp (
    echo [ERROR] src\MainWindow.cpp not found
    goto :end
)
if not exist src\CompressionEngine.cpp (
    echo [ERROR] src\CompressionEngine.cpp not found
    goto :end
)
if not exist src\FeatureCard.cpp (
    echo [ERROR] src\FeatureCard.cpp not found
    goto :end
)

echo [OK] All source files exist
echo.

REM Test CMake syntax (dry run)
echo [TEST] Testing CMake syntax...
cmake -B test_build -S . --dry-run 2>cmake_test.log
if %errorlevel% equ 0 (
    echo [OK] CMake syntax is valid
) else (
    echo [ERROR] CMake syntax error detected
    echo Error details:
    type cmake_test.log
    goto :cleanup
)

echo.
echo [SUCCESS] CMakeLists.txt configuration is correct!
echo.
echo Summary:
echo - Qt6 configuration: OK
echo - bit7z integration: OK  
echo - Source files: OK
echo - CMake syntax: OK
echo.
echo The project is ready to build once Qt6 is installed.
echo Run: .\build_qt6.bat after installing Qt6

:cleanup
if exist test_build rmdir /s /q test_build 2>nul
if exist cmake_test.log del cmake_test.log 2>nul

:end
echo.
pause

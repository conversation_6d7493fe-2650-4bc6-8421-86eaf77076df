@echo off
echo.
echo ========================================
echo     Fix Ninja Build Issues
echo ========================================
echo.

echo [INFO] This script will help fix ninja build issues
echo.

echo [STEP 1] Cleaning all build directories...
if exist build rmdir /s /q build 2>nul
if exist build-qt6 rmdir /s /q build-qt6 2>nul
if exist build-mingw rmdir /s /q build-mingw 2>nul
if exist cmake-build-debug rmdir /s /q cmake-build-debug 2>nul

echo [OK] Build directories cleaned

echo.
echo [STEP 2] Removing CMake cache files...
if exist CMakeCache.txt del CMakeCache.txt 2>nul
if exist CMakeFiles rmdir /s /q CMakeFiles 2>nul

echo [OK] CMake cache cleaned

echo.
echo [STEP 3] Instructions for Qt Creator:
echo.
echo Please follow these steps in Qt Creator:
echo.
echo 1. Close Qt Creator completely
echo 2. Reopen Qt Creator
echo 3. File → Open File or Project → CMakeLists.txt
echo 4. When configuring the project:
echo    - Select "Desktop Qt 6.9.0 MinGW 64-bit" kit
echo    - IMPORTANT: Change Generator from "Ninja" to "MinGW Makefiles"
echo    - Set build directory to: %CD%\build-mingw
echo 5. Click "Configure Project"
echo 6. Build → Clean All
echo 7. Build → Rebuild All
echo.

echo [STEP 4] Alternative: Manual CMake command
echo.
echo If Qt Creator still has issues, you can try this manual command:
echo.
echo mkdir build-manual
echo cd build-manual
echo cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug -DCMAKE_PREFIX_PATH="D:/Qt/6.9.0/mingw_64"
echo mingw32-make
echo.

echo [STEP 5] How to change generator in Qt Creator:
echo.
echo Method A - During project configuration:
echo   When you open CMakeLists.txt, in the configure dialog:
echo   - Expand "Details" section
echo   - Find "Generator" dropdown
echo   - Change from "Ninja" to "MinGW Makefiles"
echo.
echo Method B - In existing project:
echo   1. Projects tab (left sidebar)
echo   2. Build Settings
echo   3. CMake section
echo   4. Click "Change" next to Generator
echo   5. Select "MinGW Makefiles"
echo   6. Click "Apply Configuration Changes"
echo.

echo ========================================
echo     Summary
echo ========================================
echo.
echo The main issue is that ninja build failed.
echo Solution: Use MinGW Makefiles instead of Ninja.
echo.
echo This is more stable and compatible with your setup.
echo.
pause

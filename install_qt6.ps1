# Qt6 自动安装脚本
param(
    [switch]$UseChocolatey = $false,
    [switch]$UseVcpkg = $false,
    [string]$InstallPath = "C:\Qt"
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Qt6 自动安装脚本" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键选择 '以管理员身份运行'" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green

# 方案1：使用Chocolatey安装
if ($UseChocolatey) {
    Write-Host ""
    Write-Host "🍫 使用Chocolatey安装Qt6..." -ForegroundColor Cyan
    
    # 检查Chocolatey是否已安装
    try {
        choco --version | Out-Null
        Write-Host "✅ Chocolatey 已安装" -ForegroundColor Green
    } catch {
        Write-Host "📦 正在安装Chocolatey..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
    }
    
    # 安装Qt6
    Write-Host "📦 正在安装Qt6..." -ForegroundColor Yellow
    choco install qt6 -y
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Qt6 安装成功" -ForegroundColor Green
    } else {
        Write-Host "❌ Qt6 安装失败" -ForegroundColor Red
        exit 1
    }
}

# 方案2：使用vcpkg安装
elseif ($UseVcpkg) {
    Write-Host ""
    Write-Host "📦 使用vcpkg安装Qt6..." -ForegroundColor Cyan
    
    $vcpkgPath = "C:\vcpkg"
    
    if (-not (Test-Path $vcpkgPath)) {
        Write-Host "📦 正在克隆vcpkg..." -ForegroundColor Yellow
        git clone https://github.com/Microsoft/vcpkg.git $vcpkgPath
        
        Write-Host "🔨 正在构建vcpkg..." -ForegroundColor Yellow
        & "$vcpkgPath\bootstrap-vcpkg.bat"
    }
    
    Write-Host "📦 正在安装Qt6..." -ForegroundColor Yellow
    & "$vcpkgPath\vcpkg.exe" install qt6[core,widgets] --triplet x64-windows
    
    # 设置环境变量
    $env:CMAKE_TOOLCHAIN_FILE = "$vcpkgPath\scripts\buildsystems\vcpkg.cmake"
    [Environment]::SetEnvironmentVariable("CMAKE_TOOLCHAIN_FILE", $env:CMAKE_TOOLCHAIN_FILE, "User")
    
    Write-Host "✅ Qt6 通过vcpkg安装成功" -ForegroundColor Green
}

# 方案3：下载官方安装器
else {
    Write-Host ""
    Write-Host "🌐 下载Qt6官方安装器..." -ForegroundColor Cyan
    
    $installerUrl = "https://download.qt.io/official_releases/online_installers/qt-unified-windows-x64-online.exe"
    $installerPath = "$env:TEMP\qt-installer.exe"
    
    try {
        Write-Host "📥 正在下载安装器..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
        
        Write-Host "✅ 安装器下载完成" -ForegroundColor Green
        Write-Host "📁 安装器位置: $installerPath" -ForegroundColor Cyan
        
        Write-Host ""
        Write-Host "🚀 正在启动Qt安装器..." -ForegroundColor Cyan
        Write-Host ""
        Write-Host "安装指南:" -ForegroundColor Yellow
        Write-Host "1. 创建Qt账户（免费）" -ForegroundColor White
        Write-Host "2. 选择开源版本" -ForegroundColor White
        Write-Host "3. 选择以下组件:" -ForegroundColor White
        Write-Host "   ✅ Qt 6.5.3 (LTS)" -ForegroundColor Green
        Write-Host "   ✅ MinGW 11.2.0 64-bit" -ForegroundColor Green
        Write-Host "   ✅ Qt Creator" -ForegroundColor Green
        Write-Host "   ✅ CMake" -ForegroundColor Green
        Write-Host "4. 安装路径建议: C:\Qt" -ForegroundColor White
        Write-Host ""
        
        # 启动安装器
        Start-Process -FilePath $installerPath -Wait
        
        # 检查安装是否成功
        $qtPaths = @(
            "C:\Qt\6.5.3\mingw_64\bin\qmake.exe",
            "C:\Qt\6.6.0\mingw_64\bin\qmake.exe",
            "C:\Qt\6.7.0\mingw_64\bin\qmake.exe"
        )
        
        $qtFound = $false
        foreach ($path in $qtPaths) {
            if (Test-Path $path) {
                $qtFound = $true
                $qtDir = Split-Path (Split-Path $path -Parent) -Parent
                Write-Host "✅ Qt6 安装成功: $qtDir" -ForegroundColor Green
                
                # 设置环境变量
                $env:CMAKE_PREFIX_PATH = $qtDir
                [Environment]::SetEnvironmentVariable("CMAKE_PREFIX_PATH", $qtDir, "User")
                
                $qtBinPath = "$qtDir\bin"
                $mingwPath = "C:\Qt\Tools\mingw1120_64\bin"
                
                # 添加到PATH
                $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
                if ($currentPath -notlike "*$qtBinPath*") {
                    $newPath = "$qtBinPath;$mingwPath;$currentPath"
                    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
                    Write-Host "✅ 环境变量已设置" -ForegroundColor Green
                }
                break
            }
        }
        
        if (-not $qtFound) {
            Write-Host "⚠️  Qt6安装可能未完成或安装到了其他位置" -ForegroundColor Yellow
            Write-Host "请手动设置环境变量:" -ForegroundColor Yellow
            Write-Host "CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64" -ForegroundColor Cyan
        }
        
    } catch {
        Write-Host "❌ 下载安装器失败: $_" -ForegroundColor Red
        Write-Host "请手动下载: $installerUrl" -ForegroundColor Yellow
        exit 1
    }
}

# 验证安装
Write-Host ""
Write-Host "🔍 验证Qt6安装..." -ForegroundColor Cyan

try {
    $qmakeVersion = qmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ qmake 可用" -ForegroundColor Green
        Write-Host $qmakeVersion -ForegroundColor Gray
    } else {
        throw "qmake not found"
    }
} catch {
    Write-Host "❌ qmake 未找到" -ForegroundColor Red
    Write-Host "请重新启动命令提示符或设置PATH环境变量" -ForegroundColor Yellow
}

try {
    $cmakeVersion = cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ CMake 可用" -ForegroundColor Green
    } else {
        Write-Host "⚠️  CMake 未找到，请安装CMake 3.16+" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  CMake 未找到" -ForegroundColor Yellow
}

try {
    $gccVersion = gcc --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ GCC 编译器可用" -ForegroundColor Green
    } else {
        Write-Host "⚠️  GCC 编译器未找到" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  GCC 编译器未找到" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Qt6安装流程完成！" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Cyan
Write-Host "1. 重新启动命令提示符" -ForegroundColor White
Write-Host "2. 运行: .\build_qt6.bat" -ForegroundColor White
Write-Host "3. 或使用Qt Creator打开项目" -ForegroundColor White
Write-Host ""

Write-Host "如果遇到问题，请参考 QT6_SETUP_GUIDE.md" -ForegroundColor Yellow
Write-Host ""

Write-Host "按任意键退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 从GitHub下载bit7z库
param(
    [switch]$Force = $false
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    下载bit7z库" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$ErrorActionPreference = "Stop"

# 检查是否已经存在
if ((Test-Path "third_party\bit7z\lib\bit7z.lib") -and -not $Force) {
    Write-Host "✅ bit7z库已存在" -ForegroundColor Green
    Write-Host "如需重新下载，请使用 -Force 参数" -ForegroundColor Yellow
    exit 0
}

try {
    Write-Host "📥 正在获取bit7z最新版本信息..." -ForegroundColor Cyan
    
    # 获取GitHub最新版本信息
    $LatestRelease = Invoke-RestMethod -Uri "https://api.github.com/repos/rikyoz/bit7z/releases/latest" -UseBasicParsing
    $Version = $LatestRelease.tag_name
    
    Write-Host "最新版本: $Version" -ForegroundColor Green
    
    # 查找Windows x64预编译版本
    $WindowsAsset = $LatestRelease.assets | Where-Object { 
        $_.name -like "*windows*" -and $_.name -like "*x64*" -and $_.name -like "*.zip"
    } | Select-Object -First 1
    
    if (-not $WindowsAsset) {
        # 如果没有预编译版本，尝试查找源码
        Write-Host "⚠️  未找到预编译版本，尝试下载源码..." -ForegroundColor Yellow
        
        $SourceAsset = $LatestRelease.assets | Where-Object { 
            $_.name -like "*.zip" -and $_.name -notlike "*windows*"
        } | Select-Object -First 1
        
        if (-not $SourceAsset) {
            # 使用源码压缩包
            $DownloadUrl = "https://github.com/rikyoz/bit7z/archive/refs/tags/$Version.zip"
            $FileName = "bit7z-$Version-source.zip"
        } else {
            $DownloadUrl = $SourceAsset.browser_download_url
            $FileName = $SourceAsset.name
        }
        
        $IsSource = $true
    } else {
        $DownloadUrl = $WindowsAsset.browser_download_url
        $FileName = $WindowsAsset.name
        $IsSource = $false
    }
    
    Write-Host "下载链接: $DownloadUrl" -ForegroundColor Cyan
    Write-Host "文件名: $FileName" -ForegroundColor Cyan
    
    # 下载文件
    $TempFile = "third_party\$FileName"
    Write-Host "📥 正在下载..." -ForegroundColor Yellow
    
    Invoke-WebRequest -Uri $DownloadUrl -OutFile $TempFile -UseBasicParsing
    Write-Host "✅ 下载完成" -ForegroundColor Green
    
    # 解压文件
    Write-Host "📦 正在解压..." -ForegroundColor Yellow
    $TempDir = "third_party\bit7z_temp"
    
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
    }
    
    Expand-Archive -Path $TempFile -DestinationPath $TempDir -Force
    
    # 查找解压后的内容
    $ExtractedDir = Get-ChildItem $TempDir | Select-Object -First 1
    
    if ($IsSource) {
        Write-Host "📝 检测到源码版本，需要编译..." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "源码编译步骤:" -ForegroundColor Cyan
        Write-Host "1. 安装Visual Studio 2019/2022" -ForegroundColor White
        Write-Host "2. 打开Developer Command Prompt" -ForegroundColor White
        Write-Host "3. 执行以下命令:" -ForegroundColor White
        Write-Host ""
        Write-Host "cd $($ExtractedDir.FullName)" -ForegroundColor Gray
        Write-Host "mkdir build" -ForegroundColor Gray
        Write-Host "cd build" -ForegroundColor Gray
        Write-Host "cmake .. -DCMAKE_BUILD_TYPE=Release" -ForegroundColor Gray
        Write-Host "cmake --build . --config Release" -ForegroundColor Gray
        Write-Host "cmake --install . --prefix ..\..\bit7z" -ForegroundColor Gray
        Write-Host ""
        Write-Host "或者使用vcpkg安装:" -ForegroundColor Yellow
        Write-Host "vcpkg install bit7z --triplet x64-windows" -ForegroundColor Gray
        
    } else {
        # 预编译版本，直接复制文件
        Write-Host "📁 正在复制文件..." -ForegroundColor Yellow
        
        $SourcePath = $ExtractedDir.FullName
        
        # 复制头文件
        if (Test-Path "$SourcePath\include") {
            Copy-Item "$SourcePath\include\*" "third_party\bit7z\include\" -Recurse -Force
            Write-Host "✅ 头文件已复制" -ForegroundColor Green
        }
        
        # 复制库文件
        if (Test-Path "$SourcePath\lib") {
            Copy-Item "$SourcePath\lib\*" "third_party\bit7z\lib\" -Recurse -Force
            Write-Host "✅ 库文件已复制" -ForegroundColor Green
        }
        
        # 检查是否有bin目录（DLL文件）
        if (Test-Path "$SourcePath\bin") {
            Copy-Item "$SourcePath\bin\*.dll" "third_party\bit7z\lib\" -Force -ErrorAction SilentlyContinue
            Write-Host "✅ DLL文件已复制" -ForegroundColor Green
        }
    }
    
    # 清理临时文件
    Remove-Item $TempFile -Force -ErrorAction SilentlyContinue
    Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
    
    Write-Host ""
    Write-Host "🔍 验证安装结果..." -ForegroundColor Cyan
    
    $Results = @()
    $Results += [PSCustomObject]@{
        File = "bit7z.hpp"
        Path = "third_party\bit7z\include\bit7z\bit7z.hpp"
        Status = if (Test-Path "third_party\bit7z\include\bit7z\bit7z.hpp") { "✅ 存在" } else { "❌ 缺失" }
    }
    
    $Results += [PSCustomObject]@{
        File = "bit7z.lib"
        Path = "third_party\bit7z\lib\bit7z.lib"
        Status = if (Test-Path "third_party\bit7z\lib\bit7z.lib") { "✅ 存在" } else { "❌ 缺失" }
    }
    
    $Results += [PSCustomObject]@{
        File = "7z.dll"
        Path = "third_party\7zip\7z.dll"
        Status = if (Test-Path "third_party\7zip\7z.dll") { "✅ 存在" } else { "❌ 缺失" }
    }
    
    $Results | Format-Table -AutoSize
    
    if ($Results | Where-Object { $_.Status -like "*缺失*" }) {
        Write-Host "⚠️  部分文件缺失" -ForegroundColor Yellow
        if ($IsSource) {
            Write-Host "请按照上述步骤编译源码，或使用vcpkg安装" -ForegroundColor Yellow
        }
    } else {
        Write-Host "🎉 bit7z集成成功！" -ForegroundColor Green
        Write-Host ""
        Write-Host "下一步:" -ForegroundColor Cyan
        Write-Host "1. 更新CMake配置: .\update_cmake.ps1" -ForegroundColor White
        Write-Host "2. 清理构建缓存: Remove-Item -Recurse build -Force" -ForegroundColor White
        Write-Host "3. 重新构建项目: .\build_qt6.bat" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ 下载失败: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "备选方案:" -ForegroundColor Yellow
    Write-Host "1. 使用vcpkg: vcpkg install bit7z --triplet x64-windows" -ForegroundColor Cyan
    Write-Host "2. 手动下载: https://github.com/rikyoz/bit7z/releases" -ForegroundColor Cyan
    Write-Host "3. 项目仍可使用7z.dll回退模式正常工作" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 好压万能压缩 - Qt5版本启动脚本
param(
    [switch]$Clean = $false
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    好压万能压缩 - Qt5版本启动脚本" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查CMake
try {
    $cmakeVersion = cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ CMake 已安装" -ForegroundColor Green
    } else {
        throw "CMake not found"
    }
} catch {
    Write-Host "❌ 错误：未找到CMake" -ForegroundColor Red
    Write-Host "请安装CMake: https://cmake.org/download/" -ForegroundColor Yellow
    exit 1
}

# 检查Qt5
$qt5Found = $false
try {
    # 尝试查找qmake
    $qmakeVersion = qmake --version 2>$null
    if ($LASTEXITCODE -eq 0 -and $qmakeVersion -match "Qt version 5") {
        $qt5Found = $true
        Write-Host "✅ Qt5 已安装" -ForegroundColor Green
    }
} catch {}

if (-not $qt5Found) {
    # 尝试查找常见的Qt5安装路径
    $qt5Paths = @(
        "C:\Qt\5.15.2\mingw81_64\bin\qmake.exe",
        "C:\Qt\5.15.1\mingw81_64\bin\qmake.exe",
        "C:\Qt\5.14.2\mingw73_64\bin\qmake.exe",
        "C:\Qt\5.12.12\mingw73_64\bin\qmake.exe"
    )
    
    foreach ($qmakePath in $qt5Paths) {
        if (Test-Path $qmakePath) {
            $qt5Found = $true
            $qt5Dir = Split-Path $qmakePath -Parent
            $env:PATH = "$qt5Dir;$env:PATH"
            Write-Host "✅ 找到Qt5: $qt5Dir" -ForegroundColor Green
            break
        }
    }
}

if (-not $qt5Found) {
    Write-Host "❌ 错误：未找到Qt5" -ForegroundColor Red
    Write-Host "请安装Qt5开发环境" -ForegroundColor Yellow
    Write-Host "推荐版本: Qt 5.15.2" -ForegroundColor Yellow
    Write-Host "下载地址: https://www.qt.io/download-qt-installer" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "或者使用在线安装器安装Qt5.15.2 + MinGW" -ForegroundColor Yellow
    exit 1
}

# 检查编译器
$compilerFound = $false
try {
    gcc --version 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        $compilerFound = $true
        Write-Host "✅ GCC编译器已安装" -ForegroundColor Green
    }
} catch {}

if (-not $compilerFound) {
    try {
        cl 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            $compilerFound = $true
            Write-Host "✅ MSVC编译器已安装" -ForegroundColor Green
        }
    } catch {}
}

if (-not $compilerFound) {
    Write-Host "❌ 错误：未找到编译器" -ForegroundColor Red
    Write-Host "请安装MinGW或Visual Studio" -ForegroundColor Yellow
    Write-Host "Qt5通常自带MinGW编译器" -ForegroundColor Yellow
    exit 1
}

# 检查7-Zip
$sevenZipPaths = @(
    "C:\Program Files\7-Zip\7z.exe",
    "C:\Program Files (x86)\7-Zip\7z.exe"
)

$sevenZipFound = $false
foreach ($path in $sevenZipPaths) {
    if (Test-Path $path) {
        $sevenZipFound = $true
        Write-Host "✅ 7-Zip 已安装" -ForegroundColor Green
        break
    }
}

if (-not $sevenZipFound) {
    Write-Host "⚠️  警告：未找到7-Zip，压缩功能可能无法正常工作" -ForegroundColor Yellow
    Write-Host "建议安装7-Zip: https://www.7-zip.org/download.html" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔨 开始构建Qt5项目..." -ForegroundColor Cyan
Write-Host ""

# 设置Qt5路径
$qt5Paths = @(
    "C:\Qt\5.15.2\mingw81_64",
    "C:\Qt\5.15.1\mingw81_64", 
    "C:\Qt\5.14.2\mingw73_64",
    "C:\Qt\5.12.12\mingw73_64"
)

foreach ($qtPath in $qt5Paths) {
    if (Test-Path $qtPath) {
        $env:CMAKE_PREFIX_PATH = $qtPath
        Write-Host "设置Qt5路径: $qtPath" -ForegroundColor Green
        break
    }
}

# 清理旧的构建文件
if ($Clean -or (Test-Path "build")) {
    if (Test-Path "build") {
        Write-Host "清理旧的构建文件..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force "build" -ErrorAction SilentlyContinue
    }
}

# 生成构建文件
Write-Host "正在生成构建文件..." -ForegroundColor Cyan
try {
    $generator = "MinGW Makefiles"
    if ($compilerFound -and (Get-Command cl -ErrorAction SilentlyContinue)) {
        $generator = "Visual Studio 16 2019"
    }
    
    cmake -B build -S . -G $generator 2>&1 | Tee-Object -FilePath "build_error.log"
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake configuration failed"
    }
    Write-Host "✅ 构建配置成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 构建配置失败！" -ForegroundColor Red
    Write-Host "错误详情:" -ForegroundColor Red
    if (Test-Path "build_error.log") {
        Get-Content "build_error.log" | Write-Host -ForegroundColor Red
    }
    Write-Host ""
    Write-Host "可能的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 确保Qt5正确安装" -ForegroundColor Yellow
    Write-Host "2. 设置CMAKE_PREFIX_PATH环境变量" -ForegroundColor Yellow  
    Write-Host "3. 检查编译器是否在PATH中" -ForegroundColor Yellow
    exit 1
}

# 编译项目
Write-Host "正在编译项目..." -ForegroundColor Cyan
try {
    cmake --build build --config Release 2>&1 | Tee-Object -FilePath "compile_error.log"
    
    if ($LASTEXITCODE -ne 0) {
        throw "Compilation failed"
    }
    Write-Host "✅ 编译成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 编译失败！" -ForegroundColor Red
    Write-Host "错误详情:" -ForegroundColor Red
    if (Test-Path "compile_error.log") {
        Get-Content "compile_error.log" | Write-Host -ForegroundColor Red
    }
    exit 1
}

# 检查可执行文件
$exePaths = @(
    "build\BandiZip.exe",
    "build\Release\BandiZip.exe",
    "build\Debug\BandiZip.exe"
)

$exeFound = $false
$exePath = ""
foreach ($path in $exePaths) {
    if (Test-Path $path) {
        $exeFound = $true
        $exePath = $path
        break
    }
}

if (-not $exeFound) {
    Write-Host "❌ 未找到可执行文件" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Qt5项目构建成功！" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 正在启动程序..." -ForegroundColor Cyan
Write-Host ""

# 运行程序
try {
    Start-Process -FilePath $exePath
    Write-Host "✅ 程序已启动！" -ForegroundColor Green
} catch {
    Write-Host "❌ 启动程序失败：$_" -ForegroundColor Red
    Write-Host "尝试直接运行: $exePath" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎯 Qt5版本功能说明:" -ForegroundColor Cyan
Write-Host "- 📂 解压: 支持ZIP、RAR、7Z、TAR、ISO格式" -ForegroundColor White
Write-Host "- 📁 压缩: 支持ZIP、7Z格式输出" -ForegroundColor White
Write-Host "- 🎬 视频压缩: 开发中" -ForegroundColor Gray
Write-Host "- 🖼️ 图片压缩: 开发中" -ForegroundColor Gray
Write-Host "- 📄 PDF压缩: 开发中" -ForegroundColor Gray
Write-Host ""
Write-Host "使用提示:" -ForegroundColor Cyan
Write-Host "- 可以直接拖拽文件到窗口进行操作" -ForegroundColor White
Write-Host "- 点击对应卡片选择文件进行处理" -ForegroundColor White
Write-Host "- Qt5版本兼容性更好，更容易安装" -ForegroundColor White
Write-Host ""

Write-Host "按任意键退出..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

@echo off
echo.
echo ========================================
echo     Setup 7-Zip for BandiZip
echo ========================================
echo.

echo [INFO] This script will help you set up 7-Zip for RAR support in BandiZip
echo.

REM Check if 7-Zip is already installed
echo [STEP 1] Checking for existing 7-Zip installation...
if exist "C:\Program Files\7-Zip\7z.exe" (
    echo [OK] Found 7-Zip at: C:\Program Files\7-Zip\7z.exe
    echo [INFO] Your BandiZip should now support RAR files!
    goto :test
)

if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    echo [OK] Found 7-Zip at: C:\Program Files ^(x86^)\7-Zip\7z.exe
    echo [INFO] Your BandiZip should now support RAR files!
    goto :test
)

echo [INFO] 7-Zip not found in standard locations
echo.

REM Create tools directory
echo [STEP 2] Creating tools directory...
if not exist "tools" mkdir tools
echo [OK] Tools directory ready

echo.
echo [STEP 3] Download options:
echo.
echo Option 1: Download and install full 7-Zip (Recommended)
echo   - Go to: https://www.7-zip.org/download.html
echo   - Download: 7-Zip for Windows x64
echo   - Install normally
echo.
echo Option 2: Download portable 7-Zip manually
echo   - Go to: https://portableapps.com/apps/utilities/7-zip_portable
echo   - Download and extract to: %CD%\tools\
echo.
echo Option 3: Use WinRAR if available
echo   - BandiZip can also use WinRAR for RAR files
echo.

REM Check for WinRAR
echo [STEP 4] Checking for WinRAR...
if exist "C:\Program Files\WinRAR\WinRAR.exe" (
    echo [OK] Found WinRAR at: C:\Program Files\WinRAR\WinRAR.exe
    echo [INFO] You can use WinRAR for RAR files
) else if exist "C:\Program Files (x86)\WinRAR\WinRAR.exe" (
    echo [OK] Found WinRAR at: C:\Program Files ^(x86^)\WinRAR\WinRAR.exe
    echo [INFO] You can use WinRAR for RAR files
) else (
    echo [INFO] WinRAR not found
)

echo.
echo [RECOMMENDATION] 
echo For best compatibility, please install 7-Zip from: https://www.7-zip.org/
echo After installation, restart BandiZip and try extracting RAR files again.
echo.

:test
echo [STEP 5] Testing RAR support...
echo Run BandiZip and try extracting your RAR file again.
echo The application should now automatically detect and use 7-Zip for RAR files.
echo.

pause

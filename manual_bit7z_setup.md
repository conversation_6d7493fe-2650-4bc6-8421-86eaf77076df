# 🔧 手动安装bit7z库

## 📥 下载步骤

### 1. 访问GitHub Release页面
```
https://github.com/rikyoz/bit7z/releases
```

### 2. 下载最新版本
查找最新的Release，下载以下文件之一：
- `bit7z-*-windows-x64.zip` (预编译版本，推荐)
- `Source code (zip)` (源码版本，需要编译)

### 3. 解压文件
将下载的文件解压到临时目录

## 📁 文件复制

### 预编译版本
如果下载的是预编译版本，按以下结构复制文件：

```
下载的压缩包/
├── include/bit7z/
│   ├── bit7z.hpp          → third_party/bit7z/include/bit7z/bit7z.hpp
│   ├── bitcompressor.hpp  → third_party/bit7z/include/bit7z/bitcompressor.hpp
│   ├── bitextractor.hpp   → third_party/bit7z/include/bit7z/bitextractor.hpp
│   └── ...                → third_party/bit7z/include/bit7z/...
└── lib/
    ├── bit7z.lib          → third_party/bit7z/lib/bit7z.lib
    └── bit7z.dll          → third_party/bit7z/lib/bit7z.dll (可选)
```

### 源码版本
如果下载的是源码，需要编译：

#### 使用Visual Studio
```cmd
cd bit7z-源码目录
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=../../third_party/bit7z
cmake --build . --config Release
cmake --install .
```

#### 使用MinGW
```cmd
cd bit7z-源码目录
mkdir build
cd build
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=../../third_party/bit7z
cmake --build .
cmake --install .
```

## 🔍 验证安装

运行以下命令检查文件是否正确放置：

```powershell
# 检查头文件
if (Test-Path "third_party\bit7z\include\bit7z\bit7z.hpp") {
    Write-Host "✅ bit7z.hpp 已安装" -ForegroundColor Green
} else {
    Write-Host "❌ bit7z.hpp 缺失" -ForegroundColor Red
}

# 检查库文件
if (Test-Path "third_party\bit7z\lib\bit7z.lib") {
    Write-Host "✅ bit7z.lib 已安装" -ForegroundColor Green
    Get-Item "third_party\bit7z\lib\bit7z.lib" | Select-Object Name, Length
} else {
    Write-Host "❌ bit7z.lib 缺失" -ForegroundColor Red
}

# 检查7z.dll
if (Test-Path "third_party\7zip\7z.dll") {
    Write-Host "✅ 7z.dll 已安装" -ForegroundColor Green
} else {
    Write-Host "❌ 7z.dll 缺失" -ForegroundColor Red
}
```

## 📋 最终目录结构

正确安装后，项目目录应该是这样的：

```
BandiZip/
├── third_party/
│   ├── 7zip/
│   │   └── 7z.dll                    # 7-Zip核心库
│   └── bit7z/
│       ├── include/
│       │   └── bit7z/
│       │       ├── bit7z.hpp         # 主头文件
│       │       ├── bitcompressor.hpp # 压缩器
│       │       ├── bitextractor.hpp  # 解压器
│       │       └── ...               # 其他头文件
│       └── lib/
│           ├── bit7z.lib             # 静态库
│           └── bit7z.dll             # 动态库(可选)
├── src/
├── resources/
└── CMakeLists.txt
```

## 🚀 完成安装

安装完成后，运行以下命令：

```cmd
# 1. 更新CMake配置
.\update_cmake.ps1

# 2. 清理构建缓存
Remove-Item -Recurse -Force build -ErrorAction SilentlyContinue

# 3. 重新构建项目
.\build_qt6.bat
```

## 📊 性能优势

使用bit7z后，您将获得：

### ✅ 高性能模式
- **直接API调用** - 无进程开销
- **实时进度回调** - 精确的操作进度
- **异常处理** - 现代C++错误处理
- **内存效率** - 流式处理大文件

### ✅ 现代化接口
```cpp
// bit7z压缩示例
BitCompressor compressor(*m_bit7zLib, BitFormat::Zip);
compressor.setProgressCallback([this](uint64_t processed) {
    emit progressChanged(calculateProgress(processed));
});
compressor.compress(inputFiles, outputPath);
```

### ✅ 自动回退
如果bit7z不可用，项目会自动回退到7z.dll兼容模式，确保功能完整性。

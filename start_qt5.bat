@echo off
echo.
echo ========================================
echo     好压万能压缩 - Qt5版本启动脚本
echo ========================================
echo.

REM 检查CMake
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到CMake
    echo 请安装CMake: https://cmake.org/download/
    goto :error
)
echo [OK] CMake 已安装

REM 检查Qt5
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Qt5
    echo 请安装Qt5开发环境
    echo 推荐版本: Qt 5.15.2
    echo 下载地址: https://www.qt.io/download-qt-installer
    goto :error
)

REM 验证Qt5版本
qmake --version | findstr "Qt version 5" >nul
if %errorlevel% neq 0 (
    echo [警告] 检测到的Qt版本可能不是Qt5
    echo 请确保安装的是Qt5.15.2
)
echo [OK] Qt5 已安装

REM 检查编译器
where gcc >nul 2>&1 || where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到编译器
    echo 请安装MinGW或Visual Studio
    goto :error
)
echo [OK] 编译器已安装

REM 检查7-Zip
if not exist "C:\Program Files\7-Zip\7z.exe" (
    if not exist "C:\Program Files (x86)\7-Zip\7z.exe" (
        echo [警告] 未找到7-Zip，压缩功能可能无法正常工作
        echo 建议安装7-Zip: https://www.7-zip.org/download.html
    ) else (
        echo [OK] 7-Zip 已安装
    )
) else (
    echo [OK] 7-Zip 已安装
)

echo.
echo [构建] 开始构建Qt5项目...
echo.

REM 设置Qt5路径（尝试常见路径）
set QT5_FOUND=0
if exist "C:\Qt\5.15.2\mingw81_64" (
    set CMAKE_PREFIX_PATH=C:\Qt\5.15.2\mingw81_64
    set QT5_FOUND=1
    echo [设置] Qt5路径: C:\Qt\5.15.2\mingw81_64
)
if exist "C:\Qt\5.15.1\mingw81_64" if %QT5_FOUND%==0 (
    set CMAKE_PREFIX_PATH=C:\Qt\5.15.1\mingw81_64
    set QT5_FOUND=1
    echo [设置] Qt5路径: C:\Qt\5.15.1\mingw81_64
)
if exist "C:\Qt\5.14.2\mingw73_64" if %QT5_FOUND%==0 (
    set CMAKE_PREFIX_PATH=C:\Qt\5.14.2\mingw73_64
    set QT5_FOUND=1
    echo [设置] Qt5路径: C:\Qt\5.14.2\mingw73_64
)

REM 清理旧的构建文件
if exist build (
    echo [清理] 删除旧的构建文件...
    rmdir /s /q build
)

REM 生成构建文件
echo [构建] 正在生成构建文件...
cmake -B build -S . -G "MinGW Makefiles" 2>build_error.log
if %errorlevel% neq 0 (
    echo [错误] 构建配置失败！
    echo 错误详情:
    type build_error.log
    echo.
    echo 可能的解决方案:
    echo 1. 确保Qt5正确安装
    echo 2. 设置CMAKE_PREFIX_PATH环境变量
    echo 3. 检查编译器是否在PATH中
    goto :error
)
echo [OK] 构建配置成功

REM 编译项目
echo [构建] 正在编译项目...
cmake --build build --config Release 2>compile_error.log
if %errorlevel% neq 0 (
    echo [错误] 编译失败！
    echo 错误详情:
    type compile_error.log
    goto :error
)
echo [OK] 编译成功

REM 检查可执行文件
set EXE_FOUND=0
if exist "build\BandiZip.exe" (
    set EXE_PATH=build\BandiZip.exe
    set EXE_FOUND=1
)
if exist "build\Release\BandiZip.exe" if %EXE_FOUND%==0 (
    set EXE_PATH=build\Release\BandiZip.exe
    set EXE_FOUND=1
)
if exist "build\Debug\BandiZip.exe" if %EXE_FOUND%==0 (
    set EXE_PATH=build\Debug\BandiZip.exe
    set EXE_FOUND=1
)

if %EXE_FOUND%==0 (
    echo [错误] 未找到可执行文件
    goto :error
)

echo.
echo [成功] Qt5项目构建成功！
echo.
echo [启动] 正在启动程序...
echo.

REM 运行程序
start "" "%EXE_PATH%"
if %errorlevel% neq 0 (
    echo [错误] 启动程序失败
    echo 尝试直接运行: %EXE_PATH%
    goto :error
)

echo [OK] 程序已启动！
echo.
echo Qt5版本功能说明:
echo - 解压: 支持ZIP、RAR、7Z、TAR、ISO格式
echo - 压缩: 支持ZIP、7Z格式输出
echo - 视频压缩: 开发中
echo - 图片压缩: 开发中
echo - PDF压缩: 开发中
echo.
echo 使用提示:
echo - 可以直接拖拽文件到窗口进行操作
echo - 点击对应卡片选择文件进行处理
echo - Qt5版本兼容性更好，更容易安装
echo.
goto :end

:error
echo.
echo 解决方案:
echo 1. 参考 QT5_INSTALL_GUIDE.md 安装Qt5开发环境
echo 2. 确保所有依赖都已正确安装
echo 3. 检查环境变量设置是否正确
echo.
pause
exit /b 1

:end
echo 按任意键退出...
pause >nul

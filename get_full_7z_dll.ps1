# PowerShell script to get full 7z.dll for complete bit7z support
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     Get Full 7z.dll for BandiZip" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "To get complete bit7z support (including RAR, ZIP, etc.), you need the full 7z.dll." -ForegroundColor Yellow
Write-Host "The 7za.dll we have only supports 7z format." -ForegroundColor Yellow
Write-Host ""

Write-Host "Options to get full 7z.dll:" -ForegroundColor Green
Write-Host ""

Write-Host "Option 1: Install 7-Zip (Recommended)" -ForegroundColor Cyan
Write-Host "1. Go to: https://www.7-zip.org/download.html" -ForegroundColor White
Write-Host "2. Download: '7-Zip for Windows x64'" -ForegroundColor White
Write-Host "3. Install it normally" -ForegroundColor White
Write-Host "4. BandiZip will automatically detect and use the system 7z.dll" -ForegroundColor White
Write-Host ""

Write-Host "Option 2: Download and extract 7z.dll manually" -ForegroundColor Cyan
Write-Host "1. Download 7-Zip installer (without installing)" -ForegroundColor White
Write-Host "2. Extract 7z.dll from the installer" -ForegroundColor White
Write-Host "3. Copy it to your project" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Do you want to download the 7-Zip installer now? (y/n)"

if ($choice -eq 'y' -or $choice -eq 'Y') {
    Write-Host ""
    Write-Host "[STEP 1] Downloading 7-Zip installer..." -ForegroundColor Yellow
    
    $url = "https://www.7-zip.org/a/7z2409-x64.exe"
    $installerPath = "$env:TEMP\7z-installer.exe"
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $installerPath -UseBasicParsing
        Write-Host "✅ Downloaded: $installerPath" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "[STEP 2] Options for extracting 7z.dll:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "A. Install 7-Zip normally (Recommended)" -ForegroundColor Cyan
        Write-Host "   - Run: $installerPath" -ForegroundColor White
        Write-Host "   - Install to default location" -ForegroundColor White
        Write-Host "   - BandiZip will automatically find and use 7z.dll" -ForegroundColor White
        Write-Host ""
        Write-Host "B. Extract 7z.dll without installing" -ForegroundColor Cyan
        Write-Host "   - Use 7za.exe to extract from installer" -ForegroundColor White
        Write-Host "   - Copy 7z.dll to project manually" -ForegroundColor White
        Write-Host ""
        
        $installChoice = Read-Host "Choose option (A/B)"
        
        if ($installChoice -eq 'A' -or $installChoice -eq 'a') {
            Write-Host ""
            Write-Host "Starting 7-Zip installer..." -ForegroundColor Green
            Start-Process $installerPath -Wait
            
            # Check if 7z.dll is now available
            $systemDll = "C:\Program Files\7-Zip\7z.dll"
            if (Test-Path $systemDll) {
                Write-Host "✅ 7-Zip installed successfully!" -ForegroundColor Green
                Write-Host "✅ 7z.dll found at: $systemDll" -ForegroundColor Green
                
                # Copy to project
                $targetPath = "third_party\7zip"
                if (!(Test-Path $targetPath)) {
                    New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                }
                
                Copy-Item $systemDll "$targetPath\7z.dll" -Force
                Write-Host "✅ 7z.dll copied to project: $targetPath\7z.dll" -ForegroundColor Green
                
                # Copy to build directory
                if (Test-Path "build") {
                    Copy-Item $systemDll "build\7z.dll" -Force
                    Write-Host "✅ 7z.dll copied to build: build\7z.dll" -ForegroundColor Green
                }
                
                Write-Host ""
                Write-Host "🎉 SUCCESS! Full 7z.dll is now available for bit7z" -ForegroundColor Green
                Write-Host "   bit7z will now support ALL formats: ZIP, RAR, 7Z, TAR, ISO, etc." -ForegroundColor Green
                
            } else {
                Write-Host "❌ 7z.dll not found after installation" -ForegroundColor Red
            }
            
        } elseif ($installChoice -eq 'B' -or $installChoice -eq 'b') {
            Write-Host ""
            Write-Host "[STEP 3] Extracting 7z.dll from installer..." -ForegroundColor Yellow
            
            $sevenZaPath = "third_party\7z-extra\x64\7za.exe"
            if (Test-Path $sevenZaPath) {
                $extractPath = "$env:TEMP\7z-installer-extract"
                if (Test-Path $extractPath) {
                    Remove-Item -Recurse -Force $extractPath
                }
                New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
                
                # Extract installer
                & $sevenZaPath x $installerPath -o"$extractPath" -y
                
                # Find 7z.dll
                $dllPath = Get-ChildItem -Path $extractPath -Name "7z.dll" -Recurse | Select-Object -First 1
                if ($dllPath) {
                    $sourceDll = Join-Path $extractPath $dllPath
                    
                    # Copy to project
                    $targetPath = "third_party\7zip"
                    if (!(Test-Path $targetPath)) {
                        New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                    }
                    
                    Copy-Item $sourceDll "$targetPath\7z.dll" -Force
                    Write-Host "✅ 7z.dll extracted to: $targetPath\7z.dll" -ForegroundColor Green
                    
                    # Copy to build directory
                    if (Test-Path "build") {
                        Copy-Item $sourceDll "build\7z.dll" -Force
                        Write-Host "✅ 7z.dll copied to build: build\7z.dll" -ForegroundColor Green
                    }
                    
                    Write-Host ""
                    Write-Host "🎉 SUCCESS! Full 7z.dll extracted and ready for bit7z" -ForegroundColor Green
                    
                    # Cleanup
                    Remove-Item -Recurse -Force $extractPath -ErrorAction SilentlyContinue
                    
                } else {
                    Write-Host "❌ 7z.dll not found in installer" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ 7za.exe not found for extraction" -ForegroundColor Red
            }
        }
        
        # Cleanup installer
        Remove-Item $installerPath -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "Manual steps:" -ForegroundColor Yellow
    Write-Host "1. Visit: https://www.7-zip.org/download.html" -ForegroundColor White
    Write-Host "2. Download and install 7-Zip" -ForegroundColor White
    Write-Host "3. Rebuild your BandiZip project" -ForegroundColor White
    Write-Host "4. bit7z will automatically use the system 7z.dll" -ForegroundColor White
}

Write-Host ""
Write-Host "Next steps after getting 7z.dll:" -ForegroundColor Yellow
Write-Host "1. Rebuild: cmake --build build --config Debug" -ForegroundColor White
Write-Host "2. Run BandiZip - bit7z should now support ALL formats!" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

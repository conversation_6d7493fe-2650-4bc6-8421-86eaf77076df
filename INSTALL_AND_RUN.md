# 🚀 安装和运行指南

## 当前状态

❌ **需要安装Qt6开发环境才能运行项目**

系统检查结果：
- ✅ CMake 4.0.2 已安装
- ❌ Qt6 未安装
- ❌ 编译器未安装

## 📥 快速安装方案

### 方案1：Qt官方安装器（推荐）

1. **下载Qt安装器**
   ```
   访问：https://www.qt.io/download-qt-installer
   下载：qt-unified-windows-x64-online.exe
   ```

2. **安装Qt6组件**
   - 创建Qt账户（免费）
   - 选择开源版本
   - 安装组件：
     - ✅ Qt 6.5.0 或更高版本
     - ✅ MinGW 11.2.0 64-bit 编译器
     - ✅ Qt Creator IDE
     - ✅ CMake 工具

3. **验证安装**
   ```cmd
   # 打开新的命令提示符
   qmake --version
   gcc --version
   ```

### 方案2：使用Qt Creator（最简单）

1. **安装Qt Creator**
   - 下载并安装Qt Creator
   - 自动包含Qt6和编译器

2. **打开项目**
   - 启动Qt Creator
   - 选择 "Open Project"
   - 选择项目根目录的 `CMakeLists.txt`
   - 配置构建套件
   - 点击运行按钮

## 🏃‍♂️ 安装完成后运行

### 使用Qt Creator（推荐）
1. 打开Qt Creator
2. 打开项目 `CMakeLists.txt`
3. 点击绿色运行按钮

### 使用命令行
```cmd
# 设置Qt路径（根据实际安装路径调整）
set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64

# 构建项目
cmake -B build -S . -G "MinGW Makefiles"
cmake --build build --config Release

# 运行程序
.\build\BandiZip.exe
```

## 🎯 预期效果

程序启动后您将看到：

```
┌─────────────────────────────────────────────────────────────────┐
│ 📦 好压万能压缩                    立即登录  立即开通  ─  □  ✕ │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│    │     📂      │  │     📁      │  │     🎬      │           │
│    │             │  │             │  │             │           │
│    │    解压     │  │    压缩     │  │  视频压缩   │           │
│    │点击/拖入压缩│  │点击/拖入文件│  │支持MP4、WMV │           │
│    │包，一键快速 │  │，支持快速压│  │、AVI等多种  │           │
│    │解压         │  │缩为Zip、7z  │  │格式         │           │
│    └─────────────┘  └─────────────┘  └─────────────┘           │
│                                                                 │
│    ┌─────────────┐  ┌─────────────┐                            │
│    │     🖼️      │  │     📄      │                            │
│    │             │  │             │                            │
│    │  图片压缩   │  │  PDF压缩    │                            │
│    │支持JPG、PNG │  │高效压缩，保 │                            │
│    │、GIF、BMP等 │  │持文档质量   │                            │
│    │多种格式     │  │             │                            │
│    └─────────────┘  └─────────────┘                            │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 功能测试

### 立即可用的功能：
1. **📂 解压功能**
   - 点击绿色"解压"卡片
   - 选择ZIP/RAR/7Z/TAR/ISO文件
   - 选择解压目录
   - 查看解压进度

2. **📁 压缩功能**
   - 点击蓝色"压缩"卡片
   - 选择要压缩的文件
   - 选择输出格式(ZIP/7Z)
   - 查看压缩进度

3. **拖拽操作**
   - 直接拖拽压缩包到窗口 → 自动解压
   - 拖拽文件到窗口 → 自动压缩

### 开发中的功能：
- 🎬 视频压缩（显示"功能开发中"提示）
- 🖼️ 图片压缩（显示"功能开发中"提示）
- 📄 PDF压缩（显示"功能开发中"提示）

## 📋 安装7-Zip（可选）

为了获得最佳压缩体验，建议安装7-Zip：

1. **下载7-Zip**
   ```
   访问：https://www.7-zip.org/download.html
   下载：7z2301-x64.exe
   ```

2. **安装到默认路径**
   ```
   默认路径：C:\Program Files\7-Zip\
   ```

## 🆘 常见问题

### Q: 提示"找不到Qt6"
**A:** 确保Qt6正确安装并设置环境变量
```cmd
set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\mingw_64
```

### Q: 编译错误
**A:** 检查是否安装了MinGW编译器
```cmd
gcc --version
```

### Q: 程序无法启动
**A:** 确保所有Qt6 DLL文件在PATH中，或使用Qt Creator运行

### Q: 图标不显示
**A:** 当前使用占位符图标，可以替换resources/icons/中的PNG文件

## 🎉 成功标志

如果看到以下内容，说明项目启动成功：
- ✅ 现代化的卡片式界面
- ✅ 5个功能卡片（解压、压缩、视频、图片、PDF）
- ✅ 浅蓝色渐变背景
- ✅ 可点击和拖拽操作
- ✅ 悬停时的动画效果

## 📞 需要帮助？

如果遇到问题：
1. 检查Qt6是否正确安装
2. 确认编译器在PATH中
3. 参考BUILD_GUIDE.md了解详细步骤
4. 使用Qt Creator可以避免大部分环境配置问题

---

**立即开始：下载Qt6 → 安装 → 打开Qt Creator → 运行项目！** 🚀

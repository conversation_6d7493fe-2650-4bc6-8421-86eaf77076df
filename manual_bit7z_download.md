# 🔧 手动下载bit7z库文件

## 📥 下载预编译版本

### 步骤1：访问GitHub Releases
```
https://github.com/rikyoz/bit7z/releases
```

### 步骤2：下载文件
查找最新版本，下载以下文件之一：
- `bit7z-*-windows-x64.zip` (预编译版本，推荐)
- `bit7z-*-msvc-x64.zip` (MSVC编译版本)
- `Source code (zip)` (源码，需要编译)

### 步骤3：解压和复制文件

#### 如果是预编译版本：
```
下载的压缩包/
├── include/bit7z/
│   ├── bit7zlibrary.hpp    → third_party/bit7z/include/bit7z/
│   ├── bitfileextractor.hpp → third_party/bit7z/include/bit7z/
│   ├── bitfilecompressor.hpp → third_party/bit7z/include/bit7z/
│   └── 其他.hpp文件         → third_party/bit7z/include/bit7z/
└── lib/
    ├── bit7z.lib           → third_party/bit7z/lib/bit7z.lib
    └── bit7z.dll           → third_party/bit7z/lib/bit7z.dll (可选)
```

## 🔨 从源码编译

### 如果下载的是源码：

#### 使用Visual Studio
```cmd
cd bit7z-源码目录
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=../../third_party/bit7z
cmake --build . --config Release
cmake --install .
```

#### 使用MinGW (与Qt6匹配)
```cmd
cd bit7z-源码目录
mkdir build
cd build
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=../../third_party/bit7z
cmake --build .
cmake --install .
```

## 📋 必需文件清单

### 最终目录结构
```
third_party/
├── 7zip/
│   └── 7z.dll              # 7-Zip核心库 (已有)
└── bit7z/
    ├── include/bit7z/
    │   ├── bit7zlibrary.hpp # bit7z主头文件
    │   ├── bitfileextractor.hpp # 解压器
    │   ├── bitfilecompressor.hpp # 压缩器
    │   ├── bitformat.hpp    # 格式定义
    │   └── 其他.hpp文件      # 其他头文件
    └── lib/
        ├── bit7z.lib        # 静态库 (必需)
        └── bit7z.dll        # 动态库 (可选)
```

### 验证文件
运行以下命令检查：
```powershell
# 检查头文件
Test-Path "third_party\bit7z\include\bit7z\bit7zlibrary.hpp"

# 检查库文件
Test-Path "third_party\bit7z\lib\bit7z.lib"

# 检查7z.dll
Test-Path "third_party\7zip\7z.dll"
```

## 🔗 下载链接

### 官方资源
- **GitHub**: https://github.com/rikyoz/bit7z
- **Releases**: https://github.com/rikyoz/bit7z/releases
- **文档**: https://github.com/rikyoz/bit7z/wiki

### 备选方案
如果GitHub下载困难，可以尝试：
1. **vcpkg**: `vcpkg install bit7z --triplet x64-windows`
2. **Conan**: `conan install bit7z/4.0.7@`
3. **手动编译**: 下载源码自行编译

## ⚠️ 注意事项

### 编译器兼容性
- 确保bit7z库与您的Qt6编译器匹配
- Qt6使用MinGW时，bit7z也应该用MinGW编译
- Qt6使用MSVC时，bit7z也应该用MSVC编译

### 版本兼容性
- 推荐使用bit7z 4.0+版本
- 确保与7z.dll版本兼容
- 检查是否支持您需要的压缩格式

### 文件大小参考
- `bit7z.lib`: 约2-5MB
- `bit7z.dll`: 约1-3MB (如果使用动态链接)
- 头文件总计: 约500KB

## 🆘 故障排除

### 问题1：找不到预编译版本
**解决方案**: 下载源码自行编译

### 问题2：编译器不匹配
**解决方案**: 使用与Qt6相同的编译器重新编译bit7z

### 问题3：链接错误
**解决方案**: 检查库文件路径和CMake配置

### 问题4：运行时错误
**解决方案**: 确保7z.dll和bit7z.dll都在程序目录中

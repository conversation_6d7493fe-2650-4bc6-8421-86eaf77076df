# 🚀 bit7z库集成指南

## 什么是bit7z？

bit7z是一个现代化的C++库，提供了对7-Zip压缩库的高级封装：

✅ **现代C++接口** - 使用RAII和异常处理  
✅ **更好的性能** - 直接调用7z.dll，避免进程开销  
✅ **类型安全** - 强类型API，减少运行时错误  
✅ **进度回调** - 实时压缩/解压进度  
✅ **内存效率** - 流式处理，支持大文件  

## 📥 bit7z安装方案

### 方案1：使用vcpkg（推荐）

1. **安装vcpkg**
   ```cmd
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   ```

2. **安装bit7z**
   ```cmd
   .\vcpkg install bit7z --triplet x64-windows
   ```

3. **设置CMake工具链**
   ```cmd
   set CMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake
   ```

### 方案2：手动编译

1. **下载bit7z源码**
   ```cmd
   git clone https://github.com/rikyoz/bit7z.git
   cd bit7z
   ```

2. **使用CMake编译**
   ```cmd
   mkdir build
   cd build
   cmake .. -DCMAKE_BUILD_TYPE=Release
   cmake --build . --config Release
   ```

3. **安装到系统**
   ```cmd
   cmake --install . --prefix C:\bit7z
   ```

### 方案3：预编译二进制

1. **下载预编译版本**
   ```
   访问：https://github.com/rikyoz/bit7z/releases
   下载：bit7z-x.x.x-windows-x64.zip
   ```

2. **解压到指定目录**
   ```cmd
   # 解压到 C:\bit7z\
   # 目录结构：
   # C:\bit7z\
   # ├── include\bit7z\
   # └── lib\bit7z.lib
   ```

## 🔧 项目配置

### CMake配置

项目已经配置了自动检测bit7z的CMake脚本：

```cmake
# 查找bit7z库
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(BIT7Z QUIET bit7z)
endif()

# 如果没有找到bit7z，尝试使用vcpkg或手动查找
if(NOT BIT7Z_FOUND)
    find_path(BIT7Z_INCLUDE_DIR
        NAMES bit7z/bit7z.hpp bit7z.hpp
        PATHS
            ${CMAKE_PREFIX_PATH}/include
            ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include
            C:/vcpkg/installed/x64-windows/include
            C:/bit7z/include
        PATH_SUFFIXES bit7z
    )
    
    find_library(BIT7Z_LIBRARY
        NAMES bit7z bit7z64
        PATHS
            ${CMAKE_PREFIX_PATH}/lib
            ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib
            C:/vcpkg/installed/x64-windows/lib
            C:/bit7z/lib
    )
endif()
```

### 环境变量设置

```cmd
# 如果使用vcpkg
set CMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake

# 如果手动安装
set CMAKE_PREFIX_PATH=C:\bit7z

# 如果使用Qt6
set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64;C:\bit7z
```

## 🏃‍♂️ 构建项目

### 使用vcpkg
```cmd
# 设置工具链
set CMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake

# 构建项目
cmake -B build -S . -G "MinGW Makefiles"
cmake --build build --config Release
```

### 手动配置
```cmd
# 设置bit7z路径
set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\mingw_64;C:\bit7z

# 构建项目
cmake -B build -S . -G "MinGW Makefiles"
cmake --build build --config Release
```

## 🎯 功能对比

### bit7z vs 7z.exe

| 特性 | bit7z库 | 7z.exe |
|------|---------|--------|
| 性能 | 高（直接调用） | 中（进程开销） |
| 进度回调 | 实时精确 | 解析输出 |
| 错误处理 | 异常机制 | 退出码 |
| 内存使用 | 低（流式） | 高（缓冲） |
| 部署复杂度 | 中（需要库） | 低（单个exe） |
| API复杂度 | 简单（C++） | 复杂（命令行） |

### 项目中的实现

**自动回退机制：**
```cpp
#ifdef HAVE_BIT7Z
    if (m_bit7zLib) {
        // 使用bit7z库 - 高性能
        compressFilesBit7z();
    } else {
#endif
        // 回退到7z.exe - 兼容性
        compressFilesLegacy();
#ifdef HAVE_BIT7Z
    }
#endif
```

**bit7z压缩示例：**
```cpp
void CompressionWorker::compressFilesBit7z()
{
    try {
        // 创建压缩器
        BitCompressor compressor(*m_bit7zLib, BitFormat::Zip);
        
        // 设置进度回调
        compressor.setProgressCallback([this](uint64_t processed) {
            emit progressChanged(calculateProgress(processed));
        });
        
        // 执行压缩
        std::vector<std::string> inputFiles;
        for (const QString& path : m_inputPaths) {
            inputFiles.push_back(path.toStdString());
        }
        
        compressor.compress(inputFiles, m_outputPath.toStdString());
        emit finished(true, "压缩完成");
        
    } catch (const std::exception& e) {
        emit finished(false, QString("压缩失败: %1").arg(e.what()));
    }
}
```

## 🆘 常见问题

### Q: 提示"bit7z not found"
**A:** 确保bit7z正确安装并设置路径
```cmd
# 检查vcpkg安装
.\vcpkg list | findstr bit7z

# 或设置手动路径
set CMAKE_PREFIX_PATH=C:\bit7z
```

### Q: 编译错误"cannot find bit7z.hpp"
**A:** 检查头文件路径
```cmd
# 确保头文件存在
dir C:\vcpkg\installed\x64-windows\include\bit7z\bit7z.hpp
# 或
dir C:\bit7z\include\bit7z\bit7z.hpp
```

### Q: 链接错误"cannot find -lbit7z"
**A:** 检查库文件路径
```cmd
# 确保库文件存在
dir C:\vcpkg\installed\x64-windows\lib\bit7z.lib
# 或
dir C:\bit7z\lib\bit7z.lib
```

### Q: 运行时错误"7z.dll not found"
**A:** 确保7z.dll可访问
```cmd
# 复制7z.dll到程序目录
copy "C:\Program Files\7-Zip\7z.dll" .\build\
```

## 🎉 验证安装

### 检查bit7z是否正确集成

1. **构建项目**
   ```cmd
   .\build_qt6.bat
   ```

2. **查看构建输出**
   ```
   如果看到：
   -- bit7z found: C:/vcpkg/installed/x64-windows/lib/bit7z.lib
   
   说明bit7z集成成功！
   ```

3. **运行程序**
   - 程序启动后，压缩/解压操作将自动使用bit7z
   - 如果bit7z不可用，会自动回退到7z.exe
   - 在控制台可以看到使用的方法

### 性能对比测试

**bit7z模式：**
- 更快的压缩速度
- 实时进度更新
- 更低的内存占用

**7z.exe模式：**
- 兼容性更好
- 部署更简单
- 功能更完整

## 📞 技术支持

如果遇到问题：
1. 检查bit7z版本兼容性
2. 确认7z.dll版本匹配
3. 验证CMake配置正确
4. 查看构建日志中的bit7z相关信息

---

**bit7z集成 - 现代化、高性能的压缩解决方案！** 🚀

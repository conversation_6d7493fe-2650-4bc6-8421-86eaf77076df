{"version": 3, "configurePresets": [{"hidden": true, "name": "Qt", "cacheVariables": {"CMAKE_PREFIX_PATH": "$env{QTDIR}"}, "vendor": {"qt-project.org/Qt": {"checksum": "wVa86FgEkvdCTVp1/nxvrkaemJc="}}}, {"name": "mingw-debug", "displayName": "MinGW Debug Build", "description": "Debug build using MinGW Makefiles generator", "generator": "MinGW Makefiles", "binaryDir": "${sourceDir}/build-mingw", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_PREFIX_PATH": "D:/Qt/6.9.0/mingw_64;D:/Qt/6.9.0/mingw_64/lib/cmake/Qt6"}}], "vendor": {"qt-project.org/Presets": {"checksum": "v7N50zyuIbIirfEjRbYZLzLHpvc="}}}
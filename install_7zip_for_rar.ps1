# Install 7-Zip to enable RAR support in BandiZip
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     Install 7-Zip for RAR Support" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Your BandiZip detected RAR5 format but cannot extract it." -ForegroundColor Yellow
Write-Host "Installing full 7-Zip will enable RAR support." -ForegroundColor Yellow
Write-Host ""

# Download 7-Zip installer
$url = "https://www.7-zip.org/a/7z2409-x64.exe"
$installerPath = "$env:TEMP\7z-installer.exe"

Write-Host "[STEP 1] Downloading 7-Zip installer..." -ForegroundColor Green
try {
    Invoke-WebRequest -Uri $url -OutFile $installerPath -UseBasicParsing
    Write-Host "✅ Downloaded: $installerPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual installation:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://www.7-zip.org/download.html" -ForegroundColor White
    Write-Host "2. Download and install 7-Zip" -ForegroundColor White
    Write-Host "3. Restart BandiZip" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "[STEP 2] Installing 7-Zip..." -ForegroundColor Green
Write-Host "This will install 7-Zip to your system." -ForegroundColor Yellow

$install = Read-Host "Proceed with installation? (y/n)"
if ($install -eq 'y' -or $install -eq 'Y') {
    try {
        Start-Process $installerPath -Wait
        Write-Host "✅ 7-Zip installation completed" -ForegroundColor Green
        
        # Check if 7z.dll is now available
        $systemDll = "C:\Program Files\7-Zip\7z.dll"
        if (Test-Path $systemDll) {
            Write-Host "✅ 7z.dll found at: $systemDll" -ForegroundColor Green
            
            Write-Host ""
            Write-Host "🎉 SUCCESS! 7-Zip installed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Now BandiZip should be able to:" -ForegroundColor Cyan
            Write-Host "✅ Extract RAR files (including RAR5)" -ForegroundColor Green
            Write-Host "✅ Extract all other formats" -ForegroundColor Green
            Write-Host "✅ Use system 7z.dll for better compatibility" -ForegroundColor Green
            
        } else {
            Write-Host "⚠️ 7z.dll not found after installation" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "❌ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Installation cancelled." -ForegroundColor Yellow
}

# Cleanup
Remove-Item $installerPath -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart BandiZip" -ForegroundColor White
Write-Host "2. Try extracting your RAR file again" -ForegroundColor White
Write-Host "3. BandiZip will automatically use system 7-Zip" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

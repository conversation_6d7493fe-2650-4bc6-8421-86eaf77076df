@echo off
echo.
echo ========================================
echo     Simple Build for BandiZip
echo ========================================
echo.

REM Clean previous build
if exist build-simple rmdir /s /q build-simple 2>nul
mkdir build-simple

echo [INFO] Using Qt Creator's environment...
echo [INFO] Please ensure Qt Creator is installed and configured

echo.
echo [STEP 1] Creating build directory...
cd build-simple

echo.
echo [STEP 2] Running CMake with Qt Creator's settings...
echo.
echo Please run this command in Qt Creator's terminal or configure manually:
echo.
echo   1. Open Qt Creator
echo   2. File → Open File or Project → CMakeLists.txt
echo   3. Select Desktop Qt 6.9.0 MinGW 64-bit kit
echo   4. Set build directory to: %CD%
echo   5. Click Configure Project
echo   6. Build → Build All
echo.

cd ..

echo.
echo ========================================
echo     Alternative: Manual CMake Setup
echo ========================================
echo.
echo If you want to use command line, please:
echo.
echo 1. Open Qt Creator
echo 2. Go to Tools → External → Configure
echo 3. Copy the environment variables
echo 4. Use those in command line
echo.
echo Or use the Qt Creator integrated terminal:
echo   View → Terminal
echo.

pause

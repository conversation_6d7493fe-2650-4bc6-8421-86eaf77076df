@echo off
echo.
echo ========================================
echo     Diagnose Extract Issues
echo ========================================
echo.

echo [CHECK 1] 7z.exe availability...
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] 7z.exe found in PATH
    7z | findstr "7-Zip"
) else (
    echo [INFO] 7z.exe not in PATH, checking standard locations...
    
    if exist "C:\Program Files\7-Zip\7z.exe" (
        echo [OK] 7z.exe found at: C:\Program Files\7-Zip\7z.exe
        "C:\Program Files\7-Zip\7z.exe" | findstr "7-Zip"
    ) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
        echo [OK] 7z.exe found at: C:\Program Files (x86)\7-Zip\7z.exe
        "C:\Program Files (x86)\7-Zip\7z.exe" | findstr "7-Zip"
    ) else (
        echo [ERROR] 7z.exe not found anywhere
        echo Please install 7-Zip from: https://www.7-zip.org/
    )
)

echo.
echo [CHECK 2] 7z.dll in application directory...
if exist "build-clean\7z.dll" (
    echo [OK] 7z.dll found in: build-clean\7z.dll
    dir "build-clean\7z.dll" | findstr /C:"7z.dll"
) else if exist "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll" (
    echo [OK] 7z.dll found in: build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll
    dir "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\7z.dll" | findstr /C:"7z.dll"
) else (
    echo [WARNING] 7z.dll not found in build directory
    echo This may cause bit7z functionality to fail
)

echo.
echo [CHECK 3] Test extraction with 7z.exe...
echo Creating test archive...
echo test content > test.txt
if exist "C:\Program Files\7-Zip\7z.exe" (
    "C:\Program Files\7-Zip\7z.exe" a test.zip test.txt >nul 2>&1
    if exist test.zip (
        echo [OK] Test archive created
        echo Testing extraction...
        mkdir test_extract 2>nul
        "C:\Program Files\7-Zip\7z.exe" x test.zip -otest_extract -y >nul 2>&1
        if exist "test_extract\test.txt" (
            echo [OK] 7z.exe extraction works correctly
        ) else (
            echo [ERROR] 7z.exe extraction failed
        )
        rmdir /s /q test_extract 2>nul
        del test.zip 2>nul
    ) else (
        echo [ERROR] Failed to create test archive
    )
) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    "C:\Program Files (x86)\7-Zip\7z.exe" a test.zip test.txt >nul 2>&1
    if exist test.zip (
        echo [OK] Test archive created
        echo Testing extraction...
        mkdir test_extract 2>nul
        "C:\Program Files (x86)\7-Zip\7z.exe" x test.zip -otest_extract -y >nul 2>&1
        if exist "test_extract\test.txt" (
            echo [OK] 7z.exe extraction works correctly
        ) else (
            echo [ERROR] 7z.exe extraction failed
        )
        rmdir /s /q test_extract 2>nul
        del test.zip 2>nul
    ) else (
        echo [ERROR] Failed to create test archive
    )
) else (
    echo [SKIP] 7z.exe not available for testing
)
del test.txt 2>nul

echo.
echo [CHECK 4] Application executable...
if exist "build-clean\BandiZip.exe" (
    echo [OK] BandiZip.exe found in: build-clean\BandiZip.exe
) else if exist "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\BandiZip.exe" (
    echo [OK] BandiZip.exe found in: build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\BandiZip.exe
) else (
    echo [ERROR] BandiZip.exe not found
)

echo.
echo ========================================
echo     Diagnosis Summary
echo ========================================
echo.
echo To get detailed error information:
echo 1. Run BandiZip from Qt Creator
echo 2. Try to extract a file
echo 3. Check "Application Output" in Qt Creator
echo 4. Look for debug messages starting with "Extract parameters:"
echo.
echo Common solutions:
echo - Install 7-Zip if not available
echo - Check file permissions
echo - Try extracting to a different directory
echo - Ensure the archive file is not corrupted
echo.
pause

# 更新CMakeLists.txt以使用项目内的依赖库
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    更新CMake配置" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 备份原始CMakeLists.txt
if (Test-Path "CMakeLists.txt") {
    Copy-Item "CMakeLists.txt" "CMakeLists.txt.backup" -Force
    Write-Host "✅ 已备份原始CMakeLists.txt" -ForegroundColor Green
}

# 创建新的CMakeLists.txt内容
$CMakeContent = @'
cmake_minimum_required(VERSION 3.16)
project(BandiZip VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找Qt6组件
find_package(Qt6 COMPONENTS
        Core
        Gui
        Widgets
        REQUIRED)

# 设置第三方库路径
set(THIRD_PARTY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third_party")
set(BIT7Z_DIR "${THIRD_PARTY_DIR}/bit7z")
set(SEVENZIP_DIR "${THIRD_PARTY_DIR}/7zip")

# 检查项目内的bit7z
set(BIT7Z_FOUND FALSE)
if(EXISTS "${BIT7Z_DIR}/include/bit7z/bit7z.hpp" AND EXISTS "${BIT7Z_DIR}/lib/bit7z.lib")
    set(BIT7Z_FOUND TRUE)
    set(BIT7Z_INCLUDE_DIRS "${BIT7Z_DIR}/include")
    set(BIT7Z_LIBRARIES "${BIT7Z_DIR}/lib/bit7z.lib")
    message(STATUS "Found project-local bit7z: ${BIT7Z_LIBRARIES}")
else()
    # 回退到系统安装的bit7z
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(BIT7Z QUIET bit7z)
    endif()
    
    if(NOT BIT7Z_FOUND)
        find_path(BIT7Z_INCLUDE_DIR
            NAMES bit7z/bit7z.hpp bit7z.hpp
            PATHS
                ${CMAKE_PREFIX_PATH}/include
                ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include
                C:/vcpkg/installed/x64-windows/include
                C:/bit7z/include
            PATH_SUFFIXES bit7z
        )
        
        find_library(BIT7Z_LIBRARY
            NAMES bit7z bit7z64
            PATHS
                ${CMAKE_PREFIX_PATH}/lib
                ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib
                C:/vcpkg/installed/x64-windows/lib
                C:/bit7z/lib
        )
        
        if(BIT7Z_INCLUDE_DIR AND BIT7Z_LIBRARY)
            set(BIT7Z_FOUND TRUE)
            set(BIT7Z_INCLUDE_DIRS ${BIT7Z_INCLUDE_DIR})
            set(BIT7Z_LIBRARIES ${BIT7Z_LIBRARY})
            message(STATUS "Found system bit7z: ${BIT7Z_LIBRARIES}")
        endif()
    endif()
endif()

# 添加源文件
set(SOURCES
    main.cpp
    src/MainWindow.cpp
    src/CompressionEngine.cpp
    src/FeatureCard.cpp
)

set(HEADERS
    src/MainWindow.h
    src/CompressionEngine.h
    src/FeatureCard.h
)

# 添加资源文件
set(RESOURCES
    resources/BandiZip.qrc
)

add_executable(BandiZip ${SOURCES} ${HEADERS} ${RESOURCES})

# 配置bit7z
if(BIT7Z_FOUND)
    target_include_directories(BandiZip PRIVATE ${BIT7Z_INCLUDE_DIRS})
    target_link_libraries(BandiZip
            Qt6::Core
            Qt6::Gui
            Qt6::Widgets
            ${BIT7Z_LIBRARIES}
    )
    target_compile_definitions(BandiZip PRIVATE HAVE_BIT7Z)
    message(STATUS "bit7z integration enabled")
else()
    target_link_libraries(BandiZip
            Qt6::Core
            Qt6::Gui
            Qt6::Widgets
    )
    message(WARNING "bit7z not found, using fallback 7z.exe method")
endif()

# Windows部署配置
if (WIN32)
    set(DEBUG_SUFFIX)
    if (MSVC AND CMAKE_BUILD_TYPE MATCHES "Debug")
        set(DEBUG_SUFFIX "d")
    endif ()
    
    set(QT_INSTALL_PATH "${CMAKE_PREFIX_PATH}")
    if (NOT EXISTS "${QT_INSTALL_PATH}/bin")
        set(QT_INSTALL_PATH "${QT_INSTALL_PATH}/..")
        if (NOT EXISTS "${QT_INSTALL_PATH}/bin")
            set(QT_INSTALL_PATH "${QT_INSTALL_PATH}/..")
        endif ()
    endif ()
    
    # 复制Qt6平台插件
    if (EXISTS "${QT_INSTALL_PATH}/plugins/platforms/qwindows${DEBUG_SUFFIX}.dll")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E make_directory
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>/plugins/platforms/")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${QT_INSTALL_PATH}/plugins/platforms/qwindows${DEBUG_SUFFIX}.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>/plugins/platforms/")
    endif ()
    
    # 复制Qt6 DLL文件
    foreach (QT_LIB Core Gui Widgets)
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${QT_INSTALL_PATH}/bin/Qt6${QT_LIB}${DEBUG_SUFFIX}.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
    endforeach (QT_LIB)
    
    # 复制项目内的7z.dll
    if(EXISTS "${SEVENZIP_DIR}/7z.dll")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${SEVENZIP_DIR}/7z.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
        message(STATUS "Will copy project-local 7z.dll")
    else()
        # 回退到系统7z.dll
        set(SYSTEM_SEVENZIP_PATHS
            "C:/Program Files/7-Zip/7z.dll"
            "C:/Program Files (x86)/7-Zip/7z.dll"
        )
        foreach(SEVENZIP_PATH ${SYSTEM_SEVENZIP_PATHS})
            if(EXISTS ${SEVENZIP_PATH})
                add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                        COMMAND ${CMAKE_COMMAND} -E copy
                        "${SEVENZIP_PATH}"
                        "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
                message(STATUS "Will copy system 7z.dll: ${SEVENZIP_PATH}")
                break()
            endif()
        endforeach()
    endif()
    
    # 复制项目内的bit7z.dll（如果存在）
    if(EXISTS "${BIT7Z_DIR}/lib/bit7z.dll")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${BIT7Z_DIR}/lib/bit7z.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
        message(STATUS "Will copy project-local bit7z.dll")
    endif()
endif ()

# 设置输出目录
set_target_properties(BandiZip PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}"
)

# 安装配置
install(TARGETS BandiZip
    RUNTIME DESTINATION bin
)

# 安装依赖库
if(WIN32)
    if(EXISTS "${SEVENZIP_DIR}/7z.dll")
        install(FILES "${SEVENZIP_DIR}/7z.dll" DESTINATION bin)
    endif()
    
    if(EXISTS "${BIT7Z_DIR}/lib/bit7z.dll")
        install(FILES "${BIT7Z_DIR}/lib/bit7z.dll" DESTINATION bin)
    endif()
endif()
'@

# 写入新的CMakeLists.txt
$CMakeContent | Out-File -FilePath "CMakeLists.txt" -Encoding UTF8

Write-Host "✅ CMakeLists.txt 已更新" -ForegroundColor Green
Write-Host ""
Write-Host "更新内容:" -ForegroundColor Cyan
Write-Host "- 优先使用项目内的bit7z库" -ForegroundColor White
Write-Host "- 优先使用项目内的7z.dll" -ForegroundColor White
Write-Host "- 自动复制依赖到输出目录" -ForegroundColor White
Write-Host "- 支持独立部署" -ForegroundColor White
Write-Host "- 保持向后兼容性" -ForegroundColor White
Write-Host ""
Write-Host "🎉 配置更新完成！" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Cyan
Write-Host "1. 清理构建缓存: Remove-Item -Recurse build -Force" -ForegroundColor White
Write-Host "2. 重新构建项目: .\build_qt6.bat" -ForegroundColor White
Write-Host "3. 检查输出目录中的依赖文件" -ForegroundColor White

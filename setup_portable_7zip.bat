@echo off
echo.
echo ========================================
echo     Setup Portable 7-Zip
echo ========================================
echo.

echo [INFO] This will download and setup portable 7-Zip
echo.

REM Create tools directory
if not exist "tools" mkdir tools

echo [STEP 1] Downloading 7-Zip Extra (portable version)...
echo.

REM Download using PowerShell
powershell -Command "try { Write-Host 'Downloading 7-Zip Extra...'; Invoke-WebRequest -Uri 'https://www.7-zip.org/a/7z2301-extra.7z' -OutFile 'tools\7z-extra.7z' -UseBasicParsing; Write-Host 'Download completed' } catch { Write-Host 'Download failed:' $_.Exception.Message }"

if not exist "tools\7z-extra.7z" (
    echo [ERROR] Download failed
    echo.
    echo Manual download instructions:
    echo 1. Visit: https://www.7-zip.org/download.html
    echo 2. Download "7-Zip Extra: standalone console version"
    echo 3. Extract 7z.exe to tools\ directory
    goto :end
)

echo.
echo [STEP 2] Extracting 7z.exe...
echo Note: We need 7z.exe to extract the downloaded file
echo.

REM Try to find any existing 7z.exe
set FOUND_7Z=
if exist "C:\Program Files\7-Zip\7z.exe" set FOUND_7Z=C:\Program Files\7-Zip\7z.exe
if exist "C:\Program Files (x86)\7-Zip\7z.exe" set FOUND_7Z=C:\Program Files (x86)\7-Zip\7z.exe

if defined FOUND_7Z (
    echo [OK] Using existing 7z.exe: %FOUND_7Z%
    "%FOUND_7Z%" x "tools\7z-extra.7z" -otools\ -y
    if exist "tools\7z.exe" (
        echo [OK] 7z.exe extracted successfully
    ) else (
        echo [ERROR] Failed to extract 7z.exe
    )
) else (
    echo [INFO] No existing 7z.exe found
    echo.
    echo Please:
    echo 1. Install 7-Zip from https://www.7-zip.org/
    echo 2. Or manually extract tools\7z-extra.7z to get 7z.exe
)

echo.
echo [STEP 3] Copying to build directory...
if exist "tools\7z.exe" (
    if exist "build-clean" (
        copy "tools\7z.exe" "build-clean\"
        echo [OK] 7z.exe copied to build-clean\
    )
    if exist "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug" (
        copy "tools\7z.exe" "build\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\"
        echo [OK] 7z.exe copied to build directory
    )
) else (
    echo [SKIP] 7z.exe not available for copying
)

echo.
echo [VERIFICATION] Testing 7z.exe...
if exist "tools\7z.exe" (
    "tools\7z.exe" | findstr "7-Zip"
    echo [OK] 7z.exe is working
) else (
    echo [INFO] 7z.exe not available for testing
)

:end
echo.
echo ========================================
echo     Setup Complete
echo ========================================
echo.
if exist "tools\7z.exe" (
    echo [SUCCESS] Portable 7-Zip is ready!
    echo.
    echo 7z.exe location: tools\7z.exe
    echo.
    echo Your BandiZip should now be able to extract files.
    echo Try the extract function again.
) else (
    echo [INCOMPLETE] Setup not complete
    echo.
    echo Please install 7-Zip manually:
    echo https://www.7-zip.org/download.html
)
echo.
pause

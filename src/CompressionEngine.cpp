#include "CompressionEngine.h"
#include <QApplication>
#include <QStandardPaths>
#include <QDebug>
#include <QRegularExpression>
#include <QProcess>
#include <memory>
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>
#include <cstring>

// 包含UnRAR API定义
#ifdef _WIN32
#include "../third_party/unrar/unrar.h"
#endif
#ifdef _WIN32
#include <windows.h>
#include <objbase.h>
#include <vector>

// 现在使用unrar.h中的定义，移除重复定义

#endif

#ifdef HAVE_BIT7Z
using namespace bit7z;
#endif

CompressionEngine::CompressionEngine(QObject *parent)
    : QObject(parent)
    , m_worker(nullptr)
    , m_workerThread(nullptr)
    , m_operationInProgress(false)
#ifdef HAVE_BIT7Z
    , m_bit7zLib(nullptr)
    , m_hasUnRAR(false)
#endif
{
    setupCompressionLibrary();
}

CompressionEngine::~CompressionEngine()
{
    cancelOperation();
}

void CompressionEngine::setupCompressionLibrary()
{
#ifdef HAVE_BIT7Z
    // 初始化COM组件（Windows平台）
#ifdef _WIN32
    HRESULT hr = CoInitializeEx(NULL, COINIT_APARTMENTTHREADED);
    if (FAILED(hr)) {
        qWarning() << "❌ COM initialization failed:" << hr;
    } else {
        qDebug() << "✅ COM initialized successfully";
    }
#endif

    // 强制使用bit7z，不回退到7z.exe
    try {
        QStringList dllPaths = {
            // 最高优先级：项目中的完整7z.dll（我们刚下载的）
            QApplication::applicationDirPath() + "/7z.dll",
            QApplication::applicationDirPath() + "/../third_party/7zip/7z.dll",
            QDir::currentPath() + "/third_party/7zip/7z.dll",
            "third_party/7zip/7z.dll",
            // 系统安装的完整7z.dll
            "C:/Program Files/7-Zip/7z.dll",
            "C:/Program Files (x86)/7-Zip/7z.dll",
            // 最后才尝试7za.dll（只支持7z格式）
            QApplication::applicationDirPath() + "/../third_party/7z-extra/x64/7za.dll",
            QApplication::applicationDirPath() + "/../third_party/7z-extra/7za.dll",
            QDir::currentPath() + "/third_party/7z-extra/x64/7za.dll",
            QDir::currentPath() + "/third_party/7z-extra/7za.dll",
            "third_party/7z-extra/x64/7za.dll",
            "third_party/7z-extra/7za.dll"
        };

        // 同时检查UnRAR.dll的可用性（用于RAR支持）
        QStringList unrarPaths = {
            // 优先使用64位版本
            QApplication::applicationDirPath() + "/UnRAR64.dll",
            QApplication::applicationDirPath() + "/../third_party/unrar/x64/UnRAR64.dll",
            QDir::currentPath() + "/third_party/unrar/x64/UnRAR64.dll",
            "third_party/unrar/x64/UnRAR64.dll",
            // 32位版本作为备选
            QApplication::applicationDirPath() + "/UnRAR.dll",
            QApplication::applicationDirPath() + "/../third_party/unrar/UnRAR.dll",
            QDir::currentPath() + "/third_party/unrar/UnRAR.dll",
            "third_party/unrar/UnRAR.dll",
            // 系统安装的版本
            "C:/Program Files/WinRAR/UnRAR.dll",
            "C:/Program Files (x86)/WinRAR/UnRAR.dll"
        };

        QString unrarDllPath;
        for (const QString& path : unrarPaths) {
            if (QFileInfo::exists(path)) {
                unrarDllPath = path;
                qDebug() << "🔧 Found UnRAR.dll at:" << path;
                break;
            }
        }

        if (!unrarDllPath.isEmpty()) {
            m_hasUnRAR = true;
            m_unrarPath = unrarDllPath;
            qDebug() << "✅ UnRAR.dll available for RAR5 support";
        } else {
            m_hasUnRAR = false;
            qDebug() << "⚠️ UnRAR.dll not found - RAR5 support limited";
        }

        qDebug() << "🔧 Initializing bit7z for CompressionEngine...";

        for (const QString &path : dllPaths) {
            QFileInfo fileInfo(path);
            if (fileInfo.exists()) {
                QString absolutePath = fileInfo.absoluteFilePath();
                qDebug() << "🔧 Trying bit7z with:" << absolutePath;

                try {
                    m_bit7zLib = std::make_unique<Bit7zLibrary>(absolutePath.toStdString());
                    qDebug() << "✅ CompressionEngine bit7z initialized successfully!";
                    return;
                } catch (const BitException& e) {
                    qWarning() << "❌ BitException:" << e.what();
                } catch (const std::exception& e) {
                    qWarning() << "❌ Exception:" << e.what();
                }
            }
        }

        qWarning() << "❌ CompressionEngine: bit7z initialization failed";
    } catch (const std::exception& e) {
        qWarning() << "❌ CompressionEngine bit7z setup exception:" << e.what();
    }
#else
    qWarning() << "❌ HAVE_BIT7Z not defined in CompressionEngine";
#endif

    if (!m_bit7zLib) {
        qWarning() << "❌ CompressionEngine: No working compression library available";
    }
}

QString CompressionEngine::getSevenZipPath()
{
    // 优先查找7za.exe（项目自带），然后是系统7-Zip
    QStringList possiblePaths = {
        // 项目中的7za.exe（支持更多格式）
        QApplication::applicationDirPath() + "/../third_party/7z-extra/x64/7za.exe",
        QApplication::applicationDirPath() + "/../third_party/7z-extra/7za.exe",
        QDir::currentPath() + "/third_party/7z-extra/x64/7za.exe",
        QDir::currentPath() + "/third_party/7z-extra/7za.exe",
        "third_party/7z-extra/x64/7za.exe",
        "third_party/7z-extra/7za.exe",
        // 系统7-Zip安装
        "C:/Program Files/7-Zip/7z.exe",
        "C:/Program Files (x86)/7-Zip/7z.exe",
        // 项目中的其他可执行文件
        QApplication::applicationDirPath() + "/7z.exe",
        QApplication::applicationDirPath() + "/../third_party/7zip/7z.exe",
        QDir::currentPath() + "/third_party/7zip/7z.exe",
        QApplication::applicationDirPath() + "/tools/7z.exe"
    };

    for (const QString &path : possiblePaths) {
        if (QFileInfo::exists(path)) {
            qDebug() << "Found 7z.exe at:" << path;
            return path;
        }
    }

    qWarning() << "7z.exe not found in any expected location";
    return QString();
}

bool CompressionEngine::isCompressionLibraryAvailable()
{
#ifdef HAVE_BIT7Z
    return (m_bit7zLib != nullptr) || (!m_sevenZipPath.isEmpty() && QFileInfo::exists(m_sevenZipPath));
#else
    return !m_sevenZipPath.isEmpty() && QFileInfo::exists(m_sevenZipPath);
#endif
}

bool CompressionEngine::isSupportedFormat(const QString &filePath, bool forCompression)
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    
    if (forCompression) {
        // 支持压缩的格式
        return (suffix == "zip" || suffix == "7z");
    } else {
        // 支持解压的格式
        QStringList supportedFormats = {"zip", "rar", "7z", "tar", "iso"};
        return supportedFormats.contains(suffix);
    }
}

void CompressionEngine::compressFiles(const QStringList &filePaths, const QString &archivePath)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_operationInProgress) {
        emit finished(false, "另一个操作正在进行中");
        return;
    }
    
    if (!isCompressionLibraryAvailable()) {
        emit finished(false, "压缩库未找到，请安装 7-Zip 或 bit7z");
        return;
    }
    
    if (filePaths.isEmpty()) {
        emit finished(false, "没有选择文件");
        return;
    }
    
    // 检查输出格式
    if (!isSupportedFormat(archivePath, true)) {
        emit finished(false, "不支持的压缩格式");
        return;
    }
    
    m_operationInProgress = true;
    
    // 创建工作线程
    m_workerThread = new QThread(this);
    m_worker = new CompressionWorker();
    m_worker->moveToThread(m_workerThread);
    
    // 连接信号
    connect(m_workerThread, &QThread::started, m_worker, &CompressionWorker::doWork);
    connect(m_worker, &CompressionWorker::finished, this, &CompressionEngine::onWorkerFinished);
    connect(m_worker, &CompressionWorker::progressChanged, this, &CompressionEngine::onWorkerProgress);
    connect(m_workerThread, &QThread::finished, m_worker, &QObject::deleteLater);
    
    // 设置操作参数
    m_worker->setOperation(CompressionWorker::Compress, filePaths, archivePath, m_sevenZipPath);

#ifdef HAVE_BIT7Z
    if (m_bit7zLib) {
        m_worker->setBit7zLibrary(m_bit7zLib.get());
        m_worker->setUnRARInfo(m_hasUnRAR, m_unrarPath);
    }
#endif

    // 启动线程
    m_workerThread->start();
}

void CompressionEngine::extractArchive(const QString &archivePath, const QString &extractPath)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_operationInProgress) {
        emit finished(false, "另一个操作正在进行中");
        return;
    }
    
    if (!isCompressionLibraryAvailable()) {
        emit finished(false, "压缩库未找到，请安装 7-Zip 或 bit7z");
        return;
    }
    
    if (!QFileInfo::exists(archivePath)) {
        emit finished(false, "压缩文件不存在");
        return;
    }
    
    if (!isSupportedFormat(archivePath, false)) {
        emit finished(false, "不支持的压缩格式");
        return;
    }
    
    m_operationInProgress = true;
    
    // 创建工作线程
    m_workerThread = new QThread(this);
    m_worker = new CompressionWorker();
    m_worker->moveToThread(m_workerThread);
    
    // 连接信号
    connect(m_workerThread, &QThread::started, m_worker, &CompressionWorker::doWork);
    connect(m_worker, &CompressionWorker::finished, this, &CompressionEngine::onWorkerFinished);
    connect(m_worker, &CompressionWorker::progressChanged, this, &CompressionEngine::onWorkerProgress);
    connect(m_workerThread, &QThread::finished, m_worker, &QObject::deleteLater);
    
    // 设置操作参数
    QStringList inputPaths;
    inputPaths << archivePath;
    m_worker->setOperation(CompressionWorker::Extract, inputPaths, extractPath, m_sevenZipPath);

#ifdef HAVE_BIT7Z
    if (m_bit7zLib) {
        m_worker->setBit7zLibrary(m_bit7zLib.get());
        m_worker->setUnRARInfo(m_hasUnRAR, m_unrarPath);
    }
#endif

    // 启动线程
    m_workerThread->start();
}

void CompressionEngine::getArchiveInfo(const QString &archivePath)
{
    // 实现获取压缩包信息的功能
    // 这里可以添加列出压缩包内容的功能
}

void CompressionEngine::cancelOperation()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_operationInProgress && m_workerThread) {
        m_workerThread->requestInterruption();
        m_workerThread->wait(5000); // 等待5秒
        if (m_workerThread->isRunning()) {
            m_workerThread->terminate();
            m_workerThread->wait(2000);
        }
        
        delete m_workerThread;
        m_workerThread = nullptr;
        m_worker = nullptr;
        m_operationInProgress = false;
    }
}

void CompressionEngine::onWorkerFinished()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_worker) {
        bool success = m_worker->property("success").toBool();
        QString message = m_worker->property("message").toString();
        emit finished(success, message);
    }
    
    if (m_workerThread) {
        m_workerThread->quit();
        m_workerThread->wait();
        delete m_workerThread;
        m_workerThread = nullptr;
        m_worker = nullptr;
    }
    
    m_operationInProgress = false;
}

void CompressionEngine::onWorkerProgress(int percentage)
{
    emit progressChanged(percentage);
}

// CompressionWorker 实现
CompressionWorker::CompressionWorker(QObject *parent)
    : QObject(parent)
    , m_operationType(Compress)
    , m_process(nullptr)
    , m_cancelled(false)
#ifdef HAVE_BIT7Z
    , m_bit7zLib(nullptr)
    , m_hasUnRAR(false)
#endif
{
}

void CompressionWorker::setOperation(OperationType type, const QStringList &inputPaths,
                                   const QString &outputPath, const QString &sevenZipPath)
{
    m_operationType = type;
    m_inputPaths = inputPaths;
    m_outputPath = outputPath;
    m_sevenZipPath = sevenZipPath;
}

#ifdef HAVE_BIT7Z
void CompressionWorker::setBit7zLibrary(Bit7zLibrary* lib)
{
    m_bit7zLib = lib;
}

void CompressionWorker::setUnRARInfo(bool hasUnRAR, const QString& unrarPath)
{
    m_hasUnRAR = hasUnRAR;
    m_unrarPath = unrarPath;
}
#endif

void CompressionWorker::doWork()
{
#ifdef HAVE_BIT7Z
    if (m_bit7zLib) {
        // 使用bit7z库
        switch (m_operationType) {
        case Compress:
            compressFilesBit7z();
            break;
        case Extract:
            extractArchiveBit7z();
            break;
        case Info:
            getArchiveInfoBit7z();
            break;
        }
    } else {
#endif
        // 使用传统7z.exe方法
        switch (m_operationType) {
        case Compress:
            compressFilesLegacy();
            break;
        case Extract:
            extractArchiveLegacy();
            break;
        case Info:
            getArchiveInfoLegacy();
            break;
        }
#ifdef HAVE_BIT7Z
    }
#endif
}

void CompressionWorker::compressFilesLegacy()
{
    if (m_inputPaths.isEmpty() || m_outputPath.isEmpty() || m_sevenZipPath.isEmpty()) {
        setProperty("success", false);
        setProperty("message", "参数错误");
        emit finished(false, "参数错误");
        return;
    }
    
    // 构建7-Zip命令
    QStringList arguments;
    arguments << "a" << "-y"; // a = add, -y = assume yes
    
    // 根据输出文件扩展名设置压缩格式
    QFileInfo outputInfo(m_outputPath);
    QString suffix = outputInfo.suffix().toLower();
    if (suffix == "7z") {
        arguments << "-t7z";
    } else {
        arguments << "-tzip";
    }
    
    arguments << m_outputPath;
    arguments.append(m_inputPaths);
    
    // 执行压缩
    m_process = new QProcess(this);
    connect(m_process, &QProcess::readyReadStandardOutput, this, [this]() {
        QString output = m_process->readAllStandardOutput();
        parseProgress(output);
    });
    
    m_process->start(m_sevenZipPath, arguments);
    
    if (!m_process->waitForStarted()) {
        setProperty("success", false);
        setProperty("message", "无法启动7-Zip");
        emit finished(false, "无法启动7-Zip");
        return;
    }
    
    m_process->waitForFinished(-1);
    
    bool success = (m_process->exitCode() == 0);
    QString message = success ? QString("压缩完成") : QString("压缩失败: ") + QString::fromUtf8(m_process->readAllStandardError());
    
    setProperty("success", success);
    setProperty("message", message);
    emit finished(success, message);
    
    delete m_process;
    m_process = nullptr;
}

void CompressionWorker::extractArchiveLegacy()
{
    if (m_inputPaths.isEmpty() || m_outputPath.isEmpty() || m_sevenZipPath.isEmpty()) {
        QString errorMsg = "参数错误: ";
        if (m_inputPaths.isEmpty()) errorMsg += "输入路径为空; ";
        if (m_outputPath.isEmpty()) errorMsg += "输出路径为空; ";
        if (m_sevenZipPath.isEmpty()) errorMsg += "7z.exe路径为空; ";

        qDebug() << "Extract failed:" << errorMsg;
        setProperty("success", false);
        setProperty("message", errorMsg);
        emit finished(false, errorMsg);
        return;
    }
    
    QString archivePath = m_inputPaths.first();

    qDebug() << "Extract parameters:";
    qDebug() << "  Archive:" << archivePath;
    qDebug() << "  Output:" << m_outputPath;
    qDebug() << "  7z.exe:" << m_sevenZipPath;

    // 检查文件是否存在
    if (!QFileInfo::exists(archivePath)) {
        QString errorMsg = QString("压缩包文件不存在: %1").arg(archivePath);
        qDebug() << errorMsg;
        emit finished(false, errorMsg);
        return;
    }

    if (!QFileInfo::exists(m_sevenZipPath)) {
        QString errorMsg = QString("7z.exe不存在: %1").arg(m_sevenZipPath);
        qDebug() << errorMsg;
        emit finished(false, errorMsg);
        return;
    }

    // 构建7-Zip命令
    QStringList arguments;
    arguments << "x" << "-y"; // x = extract with full paths, -y = assume yes
    arguments << QString("-o%1").arg(m_outputPath); // output directory
    arguments << archivePath;

    qDebug() << "7z.exe command:" << m_sevenZipPath << arguments.join(" ");
    
    // 执行解压
    m_process = new QProcess(this);
    connect(m_process, &QProcess::readyReadStandardOutput, this, [this]() {
        QString output = m_process->readAllStandardOutput();
        parseProgress(output);
    });
    
    m_process->start(m_sevenZipPath, arguments);
    
    if (!m_process->waitForStarted()) {
        QString errorMsg = QString("无法启动7-Zip: %1").arg(m_process->errorString());
        qDebug() << errorMsg;
        setProperty("success", false);
        setProperty("message", errorMsg);
        emit finished(false, errorMsg);
        return;
    }
    
    m_process->waitForFinished(-1);
    
    bool success = (m_process->exitCode() == 0);
    QString standardOutput = m_process->readAllStandardOutput();
    QString standardError = m_process->readAllStandardError();

    qDebug() << "7z.exe exit code:" << m_process->exitCode();
    qDebug() << "7z.exe output:" << standardOutput;
    qDebug() << "7z.exe error:" << standardError;

    QString message;
    if (success) {
        message = "解压完成";
    } else {
        // 检查是否是RAR格式不支持的问题
        QString archivePath = m_inputPaths.first();
        QFileInfo fileInfo(archivePath);
        if (fileInfo.suffix().toLower() == "rar" && standardError.contains("Can not open the file as archive")) {
            message = QString("RAR格式不支持: 项目中的7za.exe不支持RAR格式。\n"
                            "解决方案:\n"
                            "1. 安装完整的7-Zip (推荐): https://www.7-zip.org/\n"
                            "2. 或使用WinRAR等支持RAR的软件\n"
                            "3. 将RAR文件转换为ZIP或7Z格式");
        } else {
            message = QString("解压失败 (退出码: %1)").arg(m_process->exitCode());
            if (!standardError.isEmpty()) {
                message += QString("\n错误信息: %1").arg(standardError);
            }
        }
    }
    
    setProperty("success", success);
    setProperty("message", message);
    emit finished(success, message);
    
    delete m_process;
    m_process = nullptr;
}

void CompressionWorker::getArchiveInfoLegacy()
{
    // 实现获取压缩包信息
}

#ifdef HAVE_BIT7Z
const BitInFormat& CompressionWorker::getFormatFromExtension(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();

    if (suffix == "7z") {
        return BitFormat::SevenZip;
    } else if (suffix == "rar") {
        return BitFormat::Rar;
    } else if (suffix == "tar") {
        return BitFormat::Tar;
    } else if (suffix == "iso") {
        return BitFormat::Iso;
    } else {
        return BitFormat::Zip; // 默认格式
    }
}

void CompressionWorker::compressFilesBit7z()
{
    try {
        if (!m_bit7zLib || m_inputPaths.isEmpty() || m_outputPath.isEmpty()) {
            emit finished(false, "参数错误");
            return;
        }

        emit progressChanged(0);

        // 确定压缩格式
        QFileInfo outputInfo(m_outputPath);
        QString suffix = outputInfo.suffix().toLower();

        const BitInOutFormat* format = &BitFormat::Zip;
        if (suffix == "7z") {
            format = &BitFormat::SevenZip;
        }

        // 创建压缩器 - 使用BitFileCompressor
        BitFileCompressor compressor(*m_bit7zLib, *format);

        // 设置进度回调 - bit7z期望返回bool的回调函数
        compressor.setProgressCallback([this](uint64_t processed_size) -> bool {
            // 简单的进度计算
            static int progress = 0;
            progress = (progress + 5) % 100;
            emit progressChanged(progress);
            return !m_cancelled; // 返回false可以取消操作
        });

        // 执行压缩
        std::vector<std::string> inputFiles;
        for (const QString& path : m_inputPaths) {
            inputFiles.push_back(path.toStdString());
        }

        compressor.compress(inputFiles, m_outputPath.toStdString());

        emit progressChanged(100);
        emit finished(true, "压缩完成");

    } catch (const BitException& e) {
        emit finished(false, QString("压缩失败: %1").arg(e.what()));
    } catch (const std::exception& e) {
        emit finished(false, QString("压缩失败: %1").arg(e.what()));
    }
}

void CompressionWorker::extractArchiveBit7z()
{
    qDebug() << "🔧 extractArchiveBit7z called";

    try {
        if (!m_bit7zLib || m_inputPaths.isEmpty() || m_outputPath.isEmpty()) {
            QString errorMsg = "bit7z解压参数错误: ";
            if (!m_bit7zLib) errorMsg += "bit7z库为空; ";
            if (m_inputPaths.isEmpty()) errorMsg += "输入路径为空; ";
            if (m_outputPath.isEmpty()) errorMsg += "输出路径为空; ";

            qDebug() << "❌" << errorMsg;
            emit finished(false, errorMsg);
            return;
        }

        emit progressChanged(0);

        // 获取压缩包路径并确定格式
        QString archivePath = m_inputPaths.first();
        qDebug() << "🔧 Archive path:" << archivePath;
        qDebug() << "🔧 Output path:" << m_outputPath;

        // 如果是RAR文件，先显示诊断信息
        QFileInfo fileInfo(archivePath);
        QString suffix = fileInfo.suffix().toLower();
        qDebug() << "🔧 File extension detected:" << suffix;

        if (suffix == "rar") {
            qDebug() << "🔍 RAR file detected, performing diagnostics...";
            QString diagnostics = performRARDiagnostics(archivePath);
            qDebug() << "📋 RAR Diagnostics:";
            qDebug() << diagnostics;

            // 检查是否是RAR文件，优先使用UnRAR.dll
            if (diagnostics.contains("RAR版本: RAR5") || diagnostics.contains("RAR版本: RAR4")) {
                qDebug() << "🔧 RAR file detected, trying UnRAR.dll...";

                // 如果有UnRAR.dll，尝试使用
                if (m_hasUnRAR) {
                    qDebug() << "🔧 Using UnRAR.dll for RAR extraction...";
                    if (extractRARWithUnRAR(archivePath)) {
                        return; // 成功使用UnRAR.dll解压
                    }
                    qDebug() << "⚠️ UnRAR.dll extraction failed, falling back to bit7z...";
                } else {
                    qDebug() << "⚠️ RAR file detected but UnRAR.dll not available";
                }
            }
        } else {
            qDebug() << "🔧 Non-RAR file, extension:" << suffix;
        }

        // 检查文件是否存在
        if (!QFileInfo::exists(archivePath)) {
            QString errorMsg = QString("压缩包文件不存在: %1").arg(archivePath);
            qDebug() << "❌" << errorMsg;
            emit finished(false, errorMsg);
            return;
        }

        // 确保输出目录存在
        QDir outputDir(m_outputPath);
        if (!outputDir.exists()) {
            if (!outputDir.mkpath(".")) {
                QString errorMsg = QString("无法创建输出目录: %1").arg(m_outputPath);
                qDebug() << "❌" << errorMsg;
                emit finished(false, errorMsg);
                return;
            }
        }

        const BitInFormat& format = getFormatFromExtension(archivePath);
        qDebug() << "🔧 Detected format for file:" << archivePath;

        // 创建解压器
        qDebug() << "🔧 Creating BitFileExtractor...";
        BitFileExtractor extractor(*m_bit7zLib, format);
        qDebug() << "✅ BitFileExtractor created successfully";

        // 设置进度回调 - bit7z期望返回bool的回调函数
        extractor.setProgressCallback([this](uint64_t processed_size) -> bool {
            static int progress = 0;
            progress = (progress + 5) % 100;
            emit progressChanged(progress);
            return !m_cancelled; // 返回false可以取消操作
        });
        qDebug() << "✅ Progress callback set";

        // 执行解压
        qDebug() << "🔧 Starting extraction...";

        // 使用UTF-8编码处理路径，特别是包含中文字符的路径
        std::string archivePathUtf8 = archivePath.toUtf8().toStdString();
        std::string outputPathUtf8 = m_outputPath.toUtf8().toStdString();

        qDebug() << "🔧 Archive path (UTF-8):" << QString::fromUtf8(archivePathUtf8.c_str());
        qDebug() << "🔧 Output path (UTF-8):" << QString::fromUtf8(outputPathUtf8.c_str());

        extractor.extract(archivePathUtf8, outputPathUtf8);
        qDebug() << "✅ Extraction completed successfully";

        emit progressChanged(100);
        emit finished(true, "解压完成");

    } catch (const BitException& e) {
        QString errorMsg = QString("bit7z解压失败 (BitException): %1").arg(e.what());
        qDebug() << "❌" << errorMsg;

        // 检查是否是路径编码问题
        QString archivePath = m_inputPaths.first();
        QFileInfo fileInfo(archivePath);

        // 尝试使用短路径名（8.3格式）来避免中文路径问题
        qDebug() << "🔧 Trying to resolve path encoding issues...";

        // 获取短路径名
        QString shortPath = getShortPathName(archivePath);
        if (!shortPath.isEmpty() && shortPath != archivePath) {
            qDebug() << "🔧 Trying with short path:" << shortPath;

            try {
                // 重新创建extractor用于短路径重试
                const BitInFormat& format = getFormatFromExtension(archivePath);
                BitFileExtractor shortPathExtractor(*m_bit7zLib, format);

                // 使用短路径重试
                std::string shortPathUtf8 = shortPath.toUtf8().toStdString();
                std::string outputPathUtf8 = m_outputPath.toUtf8().toStdString();

                shortPathExtractor.extract(shortPathUtf8, outputPathUtf8);
                qDebug() << "✅ Extraction completed successfully with short path";

                setProperty("success", true);
                setProperty("message", "解压完成");
                emit finished(true, "解压完成");
                return;

            } catch (const BitException& e2) {
                qDebug() << "❌ Short path also failed:" << e2.what();
            }
        }

        // 如果是RAR文件，提供特定的错误信息和诊断
        if (fileInfo.suffix().toLower() == "rar") {
            // 添加文件诊断信息
            QString diagnostics = performRARDiagnostics(archivePath);

            // 检查是否是RAR5格式
            if (diagnostics.contains("RAR版本: RAR5")) {
                QString unrarStatus = m_hasUnRAR ?
                    QString("UnRAR.dll已找到但API不兼容 (路径: %1)").arg(m_unrarPath) :
                    "UnRAR.dll未找到";

                errorMsg = QString("RAR5格式支持限制\n\n"
                                 "检测到RAR5格式文件。\n\n"
                                 "文件诊断:\n%1\n\n"
                                 "UnRAR状态: %2\n\n"
                                 "技术说明:\n"
                                 "- bit7z主要设计用于7z.dll接口\n"
                                 "- UnRAR.dll使用不同的API结构\n"
                                 "- 需要专门的UnRAR集成代码\n\n"
                                 "解决方案:\n"
                                 "1. 使用WinRAR或7-Zip解压此文件\n"
                                 "2. 将内容重新压缩为ZIP或7Z格式\n"
                                 "3. 使用BandiZip处理新格式文件\n\n"
                                 "建议使用ZIP或7Z格式以获得最佳兼容性。").arg(diagnostics).arg(unrarStatus);
            } else {
                errorMsg = QString("RAR文件解压失败: %1\n\n文件诊断:\n%2\n\n"
                                 "可能的原因:\n"
                                 "1. RAR文件损坏或加密\n"
                                 "2. 需要密码的加密RAR\n"
                                 "3. bit7z的RAR支持限制\n\n"
                                 "建议:\n"
                                 "- 检查文件是否完整\n"
                                 "- 尝试用WinRAR等工具验证\n"
                                 "- 如果有密码，请先解密").arg(e.what()).arg(diagnostics);
            }
        }

        emit finished(false, errorMsg);
    } catch (const std::exception& e) {
        QString errorMsg = QString("bit7z解压失败 (std::exception): %1").arg(e.what());
        qDebug() << "❌" << errorMsg;
        emit finished(false, errorMsg);
    }
}

void CompressionWorker::getArchiveInfoBit7z()
{
    try {
        if (!m_bit7zLib || m_inputPaths.isEmpty()) {
            emit finished(false, "参数错误");
            return;
        }

        // 获取压缩包路径并确定格式
        QString archivePath = m_inputPaths.first();
        const BitInFormat& format = getFormatFromExtension(archivePath);

        // 创建解压器来获取信息
        BitFileExtractor extractor(*m_bit7zLib, format);

        // 获取压缩包信息 - 使用test方法来验证压缩包
        try {
            extractor.test(archivePath.toStdString());
            QFileInfo archiveInfo(archivePath);
            QString suffix = archiveInfo.suffix().toUpper();
            emit finished(true, QString("压缩包验证成功，格式: %1").arg(suffix));
        } catch (const BitException& e) {
            emit finished(false, QString("压缩包验证失败: %1").arg(e.what()));
            return;
        }

    } catch (const BitException& e) {
        emit finished(false, QString("获取信息失败: %1").arg(e.what()));
    } catch (const std::exception& e) {
        emit finished(false, QString("获取信息失败: %1").arg(e.what()));
    }
}
#endif

void CompressionWorker::parseProgress(const QString &output)
{
    // 解析7-Zip输出中的进度信息
    // 7-Zip的进度输出格式可能因版本而异，这里提供一个基本的实现
    QRegularExpression progressRegex(R"((\d+)%)");
    QRegularExpressionMatch match = progressRegex.match(output);
    
    if (match.hasMatch()) {
        int percentage = match.captured(1).toInt();
        emit progressChanged(percentage);
    }
}

QString CompressionWorker::getShortPathName(const QString &longPath)
{
#ifdef _WIN32
    // 将QString转换为宽字符
    std::wstring longPathW = longPath.toStdWString();

    // 获取短路径名所需的缓冲区大小
    DWORD shortPathLen = GetShortPathNameW(longPathW.c_str(), nullptr, 0);
    if (shortPathLen == 0) {
        return QString(); // 失败
    }

    // 分配缓冲区并获取短路径名
    std::vector<wchar_t> shortPathBuffer(shortPathLen);
    DWORD result = GetShortPathNameW(longPathW.c_str(), shortPathBuffer.data(), shortPathLen);

    if (result == 0 || result >= shortPathLen) {
        return QString(); // 失败
    }

    // 转换回QString
    return QString::fromWCharArray(shortPathBuffer.data());
#else
    return longPath; // 非Windows平台直接返回原路径
#endif
}



bool CompressionWorker::extractRARWithUnRAR(const QString &rarPath)
{
#ifdef _WIN32
    if (!m_hasUnRAR || m_unrarPath.isEmpty()) {
        qDebug() << "❌ UnRAR.dll not available";
        return false;
    }

    qDebug() << "🔧 Using UnRAR.dll API for RAR extraction...";
    qDebug() << "🔧 UnRAR.dll path:" << m_unrarPath;

    // 简化版本：只测试DLL加载
    HMODULE hUnRAR = LoadLibraryA(m_unrarPath.toLocal8Bit().constData());
    if (!hUnRAR) {
        DWORD error = GetLastError();
        qDebug() << "❌ Failed to load UnRAR.dll, error code:" << error;
        return false;
    }

    qDebug() << "✅ UnRAR.dll loaded successfully";

    // 测试API函数是否存在 - 尝试不同的函数名
    FARPROC openFunc = GetProcAddress(hUnRAR, "RAROpenArchiveDataEx");
    if (!openFunc) {
        openFunc = GetProcAddress(hUnRAR, "RAROpenArchiveData");  // 尝试旧版本API
    }
    if (!openFunc) {
        openFunc = GetProcAddress(hUnRAR, "RAROpenArchive");      // 尝试更简单的API
    }

    FARPROC closeFunc = GetProcAddress(hUnRAR, "RARCloseArchive");
    FARPROC readFunc = GetProcAddress(hUnRAR, "RARReadHeaderEx");
    if (!readFunc) {
        readFunc = GetProcAddress(hUnRAR, "RARReadHeader");       // 尝试旧版本API
    }
    FARPROC processFunc = GetProcAddress(hUnRAR, "RARProcessFile");

    qDebug() << "🔧 API Functions:";
    qDebug() << "   Open function:" << (openFunc ? "Found" : "Not found");
    qDebug() << "   RARCloseArchive:" << (closeFunc ? "Found" : "Not found");
    qDebug() << "   Read function:" << (readFunc ? "Found" : "Not found");
    qDebug() << "   RARProcessFile:" << (processFunc ? "Found" : "Not found");

    // 显示所有可用的导出函数（调试用）
    qDebug() << "🔧 Checking common UnRAR.dll function names...";

    // 尝试常见的UnRAR.dll函数名
    QStringList commonFunctions = {
        "RAROpenArchive", "RAROpenArchiveEx", "RAROpenArchiveData", "RAROpenArchiveDataEx",
        "RARCloseArchive", "RARReadHeader", "RARReadHeaderEx", "RARProcessFile",
        "RARSetCallback", "RARSetChangeVolProc", "RARSetProcessDataProc"
    };

    for (const QString& funcName : commonFunctions) {
        FARPROC func = GetProcAddress(hUnRAR, funcName.toLocal8Bit().constData());
        if (func) {
            qDebug() << "   ✅" << funcName << "- Found";
        }
    }

    FreeLibrary(hUnRAR);

    if (openFunc && closeFunc && readFunc && processFunc) {
        qDebug() << "✅ All UnRAR.dll API functions found!";
        qDebug() << "🔧 Attempting real RAR extraction with UnRAR.dll...";

        // 现在尝试真正的RAR解压，不要释放DLL句柄
        return performRealUnRARExtraction(hUnRAR, rarPath, openFunc, closeFunc, readFunc, processFunc);
    } else {
        qDebug() << "❌ Some UnRAR.dll API functions missing";
        FreeLibrary(hUnRAR);
        emit finished(false, "UnRAR.dll不兼容：缺少必要的API函数");
        return false;
    }
#else
    qDebug() << "❌ UnRAR.dll only supported on Windows";
    return false;
#endif
}

bool CompressionWorker::performRealUnRARExtraction(HMODULE hUnRAR, const QString &rarPath, FARPROC openFunc, FARPROC closeFunc, FARPROC readFunc, FARPROC processFunc)
{
#ifdef _WIN32
    qDebug() << "🔧 Starting real UnRAR.dll extraction using official API...";

    // 使用正确的API函数指针 - 基于官方示例
    typedef HANDLE (PASCAL *RAROpenArchiveExProc)(struct RAROpenArchiveDataEx *ArchiveData);
    typedef int (PASCAL *RARCloseArchiveProc)(HANDLE hArcData);
    typedef int (PASCAL *RARReadHeaderProc)(HANDLE hArcData, struct RARHeaderData *HeaderData);
    typedef int (PASCAL *RARProcessFileProc)(HANDLE hArcData, int Operation, char *DestPath, char *DestName);

    // 获取正确的API函数 - 重新获取RAROpenArchiveEx
    RAROpenArchiveExProc RAROpenArchiveEx = (RAROpenArchiveExProc)GetProcAddress(hUnRAR, "RAROpenArchiveEx");
    RARCloseArchiveProc RARCloseArchive = (RARCloseArchiveProc)closeFunc;
    RARReadHeaderProc RARReadHeader = (RARReadHeaderProc)readFunc;
    RARProcessFileProc RARProcessFile = (RARProcessFileProc)processFunc;

    if (!RAROpenArchiveEx || !RARCloseArchive || !RARReadHeader || !RARProcessFile) {
        qDebug() << "❌ Failed to get required UnRAR.dll functions";
        qDebug() << "   RAROpenArchiveEx:" << RAROpenArchiveEx;
        qDebug() << "   RARCloseArchive:" << RARCloseArchive;
        qDebug() << "   RARReadHeader:" << RARReadHeader;
        qDebug() << "   RARProcessFile:" << RARProcessFile;
        FreeLibrary(hUnRAR);
        return false;
    }

    qDebug() << "✅ All UnRAR.dll API functions loaded successfully";

    // 准备打开RAR文件 - 使用正确的结构体
    struct RAROpenArchiveDataEx openData;
    memset(&openData, 0, sizeof(openData));

    // 使用UTF-8编码的路径
    QByteArray archivePathBytes = rarPath.toUtf8();
    openData.ArcName = archivePathBytes.data();
    openData.OpenMode = RAR_OM_EXTRACT;
    openData.CmtBuf = nullptr;
    openData.CmtBufSize = 0;

    qDebug() << "🔧 Archive path:" << archivePathBytes.data();
    qDebug() << "🔧 Opening RAR archive with RAROpenArchiveEx...";

    // 打开RAR文件
    HANDLE hArc = RAROpenArchiveEx(&openData);

    qDebug() << "🔧 RAROpenArchiveEx returned, handle:" << hArc;
    qDebug() << "🔧 Open result:" << openData.OpenResult;

    if (!hArc || openData.OpenResult != ERAR_SUCCESS) {
        qDebug() << "❌ Failed to open RAR archive, error code:" << openData.OpenResult;

        QString errorMsg;
        switch (openData.OpenResult) {
            case ERAR_NO_MEMORY: errorMsg = "内存不足"; break;
            case ERAR_BAD_DATA: errorMsg = "RAR文件数据损坏"; break;
            case ERAR_BAD_ARCHIVE: errorMsg = "不是有效的RAR文件"; break;
            case ERAR_UNKNOWN_FORMAT: errorMsg = "未知的RAR格式"; break;
            case ERAR_EOPEN: errorMsg = "无法打开文件"; break;
            case ERAR_BAD_PASSWORD: errorMsg = "密码错误"; break;
            default: errorMsg = QString("未知错误 (%1)").arg(openData.OpenResult); break;
        }

        qDebug() << "❌ Error details:" << errorMsg;
        FreeLibrary(hUnRAR);
        emit finished(false, QString("RAR文件打开失败: %1").arg(errorMsg));
        return false;
    }

    qDebug() << "✅ RAR archive opened successfully!";

    // 显示归档信息
    qDebug() << "🔧 Archive flags:" << openData.Flags;
    if (openData.Flags & 0x0001) qDebug() << "   - Volume archive";
    if (openData.Flags & 0x0008) qDebug() << "   - Solid archive";
    if (openData.Flags & 0x0080) qDebug() << "   - Encrypted headers";

    // 解压文件
    struct RARHeaderData headerData;
    int result;
    int fileCount = 0;
    int successCount = 0;

    QByteArray outputPathBytes = m_outputPath.toUtf8();

    qDebug() << "🔧 Starting file extraction to:" << outputPathBytes.data();

    while ((result = RARReadHeader(hArc, &headerData)) == ERAR_SUCCESS) {
        fileCount++;
        QString fileName = QString::fromLocal8Bit(headerData.FileName);
        qDebug() << "🔧 Processing file" << fileCount << ":" << fileName;
        qDebug() << "   Size:" << headerData.UnpSize << "bytes";

        // 解压当前文件
        int processResult = RARProcessFile(hArc, RAR_EXTRACT, const_cast<char*>(outputPathBytes.data()), nullptr);

        if (processResult == ERAR_SUCCESS) {
            successCount++;
            qDebug() << "✅ Extracted:" << fileName;
        } else {
            qDebug() << "❌ Failed to extract:" << fileName << "error code:" << processResult;
        }

        // 更新进度
        emit progressChanged((fileCount * 100) / 10);
    }

    // 关闭RAR文件
    RARCloseArchive(hArc);
    FreeLibrary(hUnRAR);

    qDebug() << "🔧 Extraction completed. Files processed:" << fileCount << "Success:" << successCount;

    if (successCount > 0) {
        qDebug() << "🎉 UnRAR.dll extraction completed successfully!";
        emit progressChanged(100);
        emit finished(true, QString("🎉 RAR解压完成！\n\n"
                                   "使用UnRAR.dll成功解压:\n"
                                   "- 处理文件: %1 个\n"
                                   "- 成功解压: %2 个\n"
                                   "- 输出目录: %3\n\n"
                                   "RAR5格式解压成功！").arg(fileCount).arg(successCount).arg(m_outputPath));
        return true;
    } else {
        QString resultMsg;
        if (result == ERAR_END_ARCHIVE) {
            resultMsg = "RAR文件处理完成，但没有文件被解压";
        } else {
            resultMsg = QString("RAR解压失败，错误代码: %1").arg(result);
        }

        qDebug() << "⚠️" << resultMsg;
        emit finished(false, resultMsg);
        return false;
    }

#else
    return false;
#endif
}

QString CompressionWorker::performRARDiagnostics(const QString &rarPath)
{
    QStringList diagnostics;
    QFileInfo fileInfo(rarPath);

    // 基本文件信息
    diagnostics << QString("文件大小: %1 bytes").arg(fileInfo.size());
    diagnostics << QString("文件存在: %1").arg(fileInfo.exists() ? "是" : "否");
    diagnostics << QString("可读取: %1").arg(fileInfo.isReadable() ? "是" : "否");

    // 检查文件头
    QFile file(rarPath);
    if (file.open(QIODevice::ReadOnly)) {
        QByteArray header = file.read(16);
        file.close();

        if (header.size() >= 7) {
            // RAR文件头检查
            if (header.startsWith("Rar!")) {
                diagnostics << "RAR签名: 有效 (RAR格式)";

                // 检查RAR版本 - 改进版本检测逻辑
                if (header.size() >= 8) {
                    // RAR文件头格式: "Rar!" + 0x1A + 0x07 + 0x00 (RAR4) 或 "Rar!" + 0x1A + 0x07 + 0x01 (RAR5)
                    unsigned char byte4 = static_cast<unsigned char>(header[4]);
                    unsigned char byte5 = static_cast<unsigned char>(header[5]);
                    unsigned char byte6 = static_cast<unsigned char>(header[6]);
                    unsigned char byte7 = static_cast<unsigned char>(header[7]);

                    diagnostics << QString("RAR文件头详细: %1").arg(QString(header.left(8).toHex()));

                    if (byte4 == 0x1A && byte5 == 0x07) {
                        if (byte6 == 0x00) {
                            diagnostics << "RAR版本: RAR4 (兼容)";
                        } else if (byte6 == 0x01) {
                            diagnostics << "RAR版本: RAR5 (可能不兼容)";
                        } else {
                            diagnostics << QString("RAR版本: 未知格式 (第6字节: 0x%1)").arg(byte6, 2, 16, QChar('0'));
                        }
                    } else {
                        diagnostics << QString("RAR版本: 非标准格式 (字节4-5: 0x%1%2)").arg(byte4, 2, 16, QChar('0')).arg(byte5, 2, 16, QChar('0'));
                    }
                } else {
                    diagnostics << "RAR版本: 文件头太短，无法确定版本";
                }
            } else {
                diagnostics << "RAR签名: 无效 (可能不是RAR文件)";
                diagnostics << QString("文件头: %1").arg(QString(header.toHex()));
            }
        } else {
            diagnostics << "文件头: 太短，无法分析";
        }
    } else {
        diagnostics << "文件头: 无法读取文件";
    }

    // 尝试用bit7z检测格式
    try {
        if (m_bit7zLib) {
            const BitInFormat& detectedFormat = getFormatFromExtension(rarPath);
            diagnostics << QString("bit7z检测格式: RAR格式已识别");
        }
    } catch (...) {
        diagnostics << "bit7z格式检测: 失败";
    }

    return diagnostics.join("\n");
}

#include "CompressionEngine.moc"

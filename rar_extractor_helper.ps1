# RAR文件解压助手
param(
    [string]$RarFile,
    [string]$OutputDir
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "     RAR文件解压助手" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if (-not $RarFile -or -not $OutputDir) {
    Write-Host "用法: rar_extractor_helper.ps1 -RarFile <RAR文件路径> -OutputDir <输出目录>" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Green
    Write-Host "  rar_extractor_helper.ps1 -RarFile 'C:\test\file.rar' -OutputDir 'C:\output'" -ForegroundColor White
    exit 1
}

Write-Host "RAR文件: $RarFile" -ForegroundColor Green
Write-Host "输出目录: $OutputDir" -ForegroundColor Green
Write-Host ""

# 检查文件是否存在
if (-not (Test-Path $RarFile)) {
    Write-Host "❌ RAR文件不存在: $RarFile" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "✅ 创建输出目录: $OutputDir" -ForegroundColor Green
}

# 尝试不同的解压工具
$success = $false

# 方法1: 尝试使用WinRAR
$winrarPaths = @(
    "C:\Program Files\WinRAR\WinRAR.exe",
    "C:\Program Files (x86)\WinRAR\WinRAR.exe",
    "D:\Program Files\WinRAR\WinRAR.exe",
    "D:\Program Files (x86)\WinRAR\WinRAR.exe"
)

foreach ($winrarPath in $winrarPaths) {
    if (Test-Path $winrarPath) {
        Write-Host "🔧 找到WinRAR: $winrarPath" -ForegroundColor Blue
        Write-Host "🔧 使用WinRAR解压..." -ForegroundColor Blue
        
        try {
            $process = Start-Process -FilePath $winrarPath -ArgumentList "x", "-y", "`"$RarFile`"", "`"$OutputDir`"" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Host "✅ WinRAR解压成功!" -ForegroundColor Green
                $success = $true
                break
            } else {
                Write-Host "❌ WinRAR解压失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ WinRAR解压出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 方法2: 尝试使用7-Zip
if (-not $success) {
    $sevenZipPaths = @(
        "C:\Program Files\7-Zip\7z.exe",
        "C:\Program Files (x86)\7-Zip\7z.exe",
        "D:\Program Files\7-Zip\7z.exe",
        "D:\Program Files (x86)\7-Zip\7z.exe"
    )
    
    foreach ($sevenZipPath in $sevenZipPaths) {
        if (Test-Path $sevenZipPath) {
            Write-Host "🔧 找到7-Zip: $sevenZipPath" -ForegroundColor Blue
            Write-Host "🔧 使用7-Zip解压..." -ForegroundColor Blue
            
            try {
                $process = Start-Process -FilePath $sevenZipPath -ArgumentList "x", "-y", "-o`"$OutputDir`"", "`"$RarFile`"" -Wait -PassThru -NoNewWindow
                if ($process.ExitCode -eq 0) {
                    Write-Host "✅ 7-Zip解压成功!" -ForegroundColor Green
                    $success = $true
                    break
                } else {
                    Write-Host "❌ 7-Zip解压失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
                }
            } catch {
                Write-Host "❌ 7-Zip解压出错: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# 方法3: 尝试使用PowerShell的Expand-Archive（对于某些RAR文件可能不工作）
if (-not $success) {
    Write-Host "🔧 尝试使用PowerShell内置解压..." -ForegroundColor Blue
    try {
        # 注意：PowerShell的Expand-Archive主要用于ZIP文件，对RAR支持有限
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        Write-Host "⚠️ PowerShell内置解压主要支持ZIP格式，RAR支持有限" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ PowerShell内置解压不支持此RAR文件" -ForegroundColor Red
    }
}

Write-Host ""
if ($success) {
    Write-Host "🎉 RAR文件解压完成!" -ForegroundColor Green
    Write-Host "📁 解压到: $OutputDir" -ForegroundColor Green
    
    # 显示解压的文件
    $extractedFiles = Get-ChildItem -Path $OutputDir -Recurse
    Write-Host ""
    Write-Host "解压的文件:" -ForegroundColor Cyan
    foreach ($file in $extractedFiles) {
        if ($file.PSIsContainer) {
            Write-Host "  📁 $($file.Name)" -ForegroundColor Blue
        } else {
            Write-Host "  📄 $($file.Name) ($([math]::Round($file.Length/1KB, 2)) KB)" -ForegroundColor White
        }
    }
} else {
    Write-Host "❌ 所有解压方法都失败了" -ForegroundColor Red
    Write-Host ""
    Write-Host "建议解决方案:" -ForegroundColor Yellow
    Write-Host "1. 安装WinRAR: https://www.win-rar.com/download.html" -ForegroundColor White
    Write-Host "2. 安装7-Zip: https://www.7-zip.org/download.html" -ForegroundColor White
    Write-Host "3. 使用在线RAR解压工具" -ForegroundColor White
    Write-Host "4. 将RAR文件转换为ZIP格式" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

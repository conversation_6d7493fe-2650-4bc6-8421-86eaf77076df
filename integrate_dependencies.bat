@echo off
chcp 65001 >nul
echo.
echo ========================================
echo     一键集成7-Zip和bit7z依赖
echo ========================================
echo.

REM 检查PowerShell是否可用
powershell -Command "Write-Host 'PowerShell可用'" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PowerShell不可用，请手动集成依赖
    echo 请参考 manual_setup.md 进行手动集成
    goto :end
)

echo [INFO] 开始自动集成依赖库...
echo.

REM 运行依赖下载脚本
echo [STEP 1] 下载和集成依赖库...
powershell -ExecutionPolicy Bypass -File setup_dependencies.ps1
if %errorlevel% neq 0 (
    echo [ERROR] 依赖下载失败
    echo 请检查网络连接或参考 manual_setup.md 手动集成
    goto :end
)

echo.
echo [STEP 2] 更新CMake配置...
powershell -ExecutionPolicy Bypass -File update_cmake.ps1
if %errorlevel% neq 0 (
    echo [ERROR] CMake配置更新失败
    goto :end
)

echo.
echo [STEP 3] 验证集成结果...

REM 检查关键文件
set ALL_OK=1

if not exist "third_party\7zip\7z.dll" (
    echo [ERROR] 7z.dll 未找到
    set ALL_OK=0
)

if not exist "third_party\bit7z\include\bit7z\bit7z.hpp" (
    echo [WARNING] bit7z头文件未找到，将使用7z.exe回退模式
)

if not exist "third_party\bit7z\lib\bit7z.lib" (
    echo [WARNING] bit7z库文件未找到，将使用7z.exe回退模式
)

if %ALL_OK%==1 (
    echo.
    echo [SUCCESS] 依赖集成完成！
    echo.
    echo 集成结果:
    if exist "third_party\7zip\7z.dll" echo - 7z.dll: 已集成
    if exist "third_party\bit7z\lib\bit7z.lib" echo - bit7z: 已集成 ^(高性能模式^)
    if not exist "third_party\bit7z\lib\bit7z.lib" echo - bit7z: 未集成 ^(将使用7z.exe回退^)
    echo.
    echo 项目现在可以独立部署，包含以下优势:
    echo - 不依赖系统安装的7-Zip
    echo - 使用最新版本的压缩库
    echo - 支持高性能bit7z接口
    echo - 自动回退到7z.exe兼容模式
    echo.
    echo 下一步:
    echo 1. 清理构建缓存: Remove-Item -Recurse build -Force
    echo 2. 重新构建项目: .\build_qt6.bat
    echo 3. 检查build目录中的依赖文件
    echo.
) else (
    echo.
    echo [ERROR] 依赖集成不完整
    echo.
    echo 解决方案:
    echo 1. 检查网络连接
    echo 2. 参考 manual_setup.md 手动下载依赖
    echo 3. 或运行: .\setup_dependencies.ps1 -Force
    echo.
)

:end
echo 按任意键退出...
pause >nul
